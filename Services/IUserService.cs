using ApartmanYonetimSistemi.Models;

namespace ApartmanYonetimSistemi.Services
{
    public class RegisterResult
    {
        public bool Success { get; set; }
        public string? ErrorMessage { get; set; }
    }

    public interface IUserService
    {
        Task<User?> AuthenticateAsync(string email, string password);
        Task<bool> CreateUserAsync(User user, string password);
        Task<User?> GetUserByEmailAsync(string email);
        Task<bool> UpdateUserAsync(User user);
        Task<bool> ChangePasswordAsync(string userId, string oldPassword, string newPassword);

        // <PERSON><PERSON>re sıfırlama metodları
        Task<bool> SendPasswordResetCodeAsync(string email);
        Task<bool> VerifyResetCodeAsync(string email, string resetCode);
        Task<bool> ResetPasswordAsync(string email, string resetCode, string newPassword);

        RegisterResult Register(string email, string password);
    }
}
