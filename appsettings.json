{"ConnectionStrings": {"DefaultConnection": "Server=(localdb)\\mssqllocaldb;Database=ApartmanYonetimDB;Trusted_Connection=true;TrustServerCertificate=true;"}, "Email": {"SmtpHost": "smtp.gmail.com", "SmtpPort": "587", "Username": "<EMAIL>", "Password": "your-app-password", "FromEmail": "<EMAIL>", "FromName": "Apartman <PERSON><PERSON><PERSON><PERSON>"}, "Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning", "Microsoft.EntityFrameworkCore": "Information"}}}