using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using ApartmanYonetimSistemi.Services;
using System.Windows;
using Microsoft.Extensions.DependencyInjection;
using System.Windows.Media;

namespace ApartmanYonetimSistemi.ViewModels
{
    public partial class LoginViewModel : BaseViewModel
    {
        private readonly IUserService _userService;

        [ObservableProperty]
        private string _email = string.Empty;

        [ObservableProperty]
        private string _password = string.Empty;

        [ObservableProperty]
        private string _errorMessage = string.Empty;

        private string _statusMessage;
        public string StatusMessage
        {
            get => _statusMessage;
            set { _statusMessage = value; OnPropertyChanged(); }
        }

        private Brush _statusColor = Brushes.Red;
        public Brush StatusColor
        {
            get => _statusColor;
            set { _statusColor = value; OnPropertyChanged(); }
        }

        public LoginViewModel(IUserService userService)
        {
            _userService = userService;
            Title = "Giriş Yap";
        }

        [RelayCommand]
        private async Task LoginAsync()
        {
            if (string.IsNullOrWhiteSpace(Email) || string.IsNullOrWhiteSpace(Password))
            {
                StatusMessage = "Email ve şifre gereklidir.";
                StatusColor = Brushes.Red;
                return;
            }

            IsBusy = true;
            StatusMessage = string.Empty;

            try
            {
                var user = await _userService.AuthenticateAsync(Email, Password);
                
                if (user != null)
                {
                    StatusMessage = "Giriş başarılı! Yönlendiriliyorsunuz...";
                    StatusColor = Brushes.Green;
                    // Başarılı giriş - Ana pencereyi aç
                    var app = (App)Application.Current;
                    var mainWindow = app.Services.GetRequiredService<MainWindow>();

                    // MainViewModel'e kullanıcı bilgisini aktar
                    if (mainWindow.DataContext is MainViewModel mainViewModel)
                    {
                        mainViewModel.SetCurrentUser(user);
                    }

                    mainWindow.Show();

                    // Login penceresini kapat
                    foreach (Window window in Application.Current.Windows)
                    {
                        if (window.GetType().Name == "LoginWindow")
                        {
                            window.Close();
                            break;
                        }
                    }
                }
                else
                {
                    StatusMessage = "Email veya şifre hatalı.";
                    StatusColor = Brushes.Red;
                }
            }
            catch (Exception ex)
            {
                StatusMessage = $"Giriş hatası: {ex.Message}";
                StatusColor = Brushes.Red;
            }
            finally
            {
                IsBusy = false;
            }
        }
    }
}
