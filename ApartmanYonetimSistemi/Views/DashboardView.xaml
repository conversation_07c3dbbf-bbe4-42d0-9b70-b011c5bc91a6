<UserControl x:Class="ApartmanYonetimSistemi.Views.DashboardView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:converters="clr-namespace:ApartmanYonetimSistemi.Converters">

    <UserControl.Resources>
        <converters:BooleanToVisibilityConverter x:Key="BoolToVisConverter"/>
        <converters:EmptyStateConverter x:Key="EmptyStateConverter"/>
    </UserControl.Resources>

    <ScrollViewer VerticalScrollBarVisibility="Auto">
        <Grid Margin="20">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
            </Grid.RowDefinitions>

            <!-- <PERSON><PERSON> Geldin <PERSON>jı -->
            <Border Grid.Row="0" Background="#2196F3" CornerRadius="10" Padding="20" Margin="0,0,0,20">
                <StackPanel>
                    <TextBlock Text="{Binding WelcomeMessage}"
                               FontSize="24"
                               FontWeight="Bold"
                               Foreground="White"
                               HorizontalAlignment="Center"/>
                    <TextBlock Text="Apartman Yönetim Sistemi'ne hoş geldiniz"
                               FontSize="14"
                               Foreground="#E3F2FD"
                               HorizontalAlignment="Center"
                               Margin="0,5,0,0"/>
                </StackPanel>
            </Border>

            <!-- Ana Özet Kartları -->
            <Grid Grid.Row="1" Margin="0,0,0,20">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>

                <!-- Toplam Siteler -->
                <Border Grid.Column="0" Style="{StaticResource CardStyle}" Background="#E3F2FD">
                    <StackPanel>
                        <TextBlock Text="🏢" FontSize="32" HorizontalAlignment="Center" Margin="0,0,0,10"/>
                        <TextBlock Text="{Binding TotalSites}" 
                                   FontSize="28" 
                                   FontWeight="Bold" 
                                   HorizontalAlignment="Center" 
                                   Foreground="#1976D2"/>
                        <TextBlock Text="Toplam Site" 
                                   FontSize="14" 
                                   HorizontalAlignment="Center" 
                                   Foreground="#666666"/>
                    </StackPanel>
                </Border>

                <!-- Toplam Apartmanlar -->
                <Border Grid.Column="1" Style="{StaticResource CardStyle}" Background="#E8F5E8">
                    <StackPanel>
                        <TextBlock Text="🏠" FontSize="32" HorizontalAlignment="Center" Margin="0,0,0,10"/>
                        <TextBlock Text="{Binding TotalApartments}" 
                                   FontSize="28" 
                                   FontWeight="Bold" 
                                   HorizontalAlignment="Center" 
                                   Foreground="#388E3C"/>
                        <TextBlock Text="Toplam Apartman" 
                                   FontSize="14" 
                                   HorizontalAlignment="Center" 
                                   Foreground="#666666"/>
                    </StackPanel>
                </Border>

                <!-- Toplam Daireler -->
                <Border Grid.Column="2" Style="{StaticResource CardStyle}" Background="#FFF3E0">
                    <StackPanel>
                        <TextBlock Text="🚪" FontSize="32" HorizontalAlignment="Center" Margin="0,0,0,10"/>
                        <TextBlock Text="{Binding TotalUnits}" 
                                   FontSize="28" 
                                   FontWeight="Bold" 
                                   HorizontalAlignment="Center" 
                                   Foreground="#F57C00"/>
                        <TextBlock Text="Toplam Daire" 
                                   FontSize="14" 
                                   HorizontalAlignment="Center" 
                                   Foreground="#666666"/>
                    </StackPanel>
                </Border>

                <!-- Aktif Kiracılar -->
                <Border Grid.Column="3" Style="{StaticResource CardStyle}" Background="#FCE4EC">
                    <StackPanel>
                        <TextBlock Text="👥" FontSize="32" HorizontalAlignment="Center" Margin="0,0,0,10"/>
                        <TextBlock Text="{Binding ActiveTenants}" 
                                   FontSize="28" 
                                   FontWeight="Bold" 
                                   HorizontalAlignment="Center" 
                                   Foreground="#C2185B"/>
                        <TextBlock Text="Aktif Kiracı" 
                                   FontSize="14" 
                                   HorizontalAlignment="Center" 
                                   Foreground="#666666"/>
                    </StackPanel>
                </Border>
            </Grid>

            <!-- İkinci Satır Kartları -->
            <Grid Grid.Row="2" Margin="0,0,0,20">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>

                <!-- Müsait Daireler -->
                <Border Grid.Column="0" Style="{StaticResource CardStyle}" Background="#E8F5E8">
                    <StackPanel>
                        <TextBlock Text="✅" FontSize="32" HorizontalAlignment="Center" Margin="0,0,0,10"/>
                        <TextBlock Text="{Binding AvailableUnits}"
                                   FontSize="28"
                                   FontWeight="Bold"
                                   HorizontalAlignment="Center"
                                   Foreground="#4CAF50"/>
                        <TextBlock Text="Müsait Daire"
                                   FontSize="14"
                                   HorizontalAlignment="Center"
                                   Foreground="#666666"/>
                    </StackPanel>
                </Border>

                <!-- Dolu Daireler -->
                <Border Grid.Column="1" Style="{StaticResource CardStyle}" Background="#FFEBEE">
                    <StackPanel>
                        <TextBlock Text="🏠" FontSize="32" HorizontalAlignment="Center" Margin="0,0,0,10"/>
                        <TextBlock Text="{Binding OccupiedUnits}"
                                   FontSize="28"
                                   FontWeight="Bold"
                                   HorizontalAlignment="Center"
                                   Foreground="#F44336"/>
                        <TextBlock Text="Dolu Daire"
                                   FontSize="14"
                                   HorizontalAlignment="Center"
                                   Foreground="#666666"/>
                    </StackPanel>
                </Border>

                <!-- Aylık Gelir -->
                <Border Grid.Column="2" Style="{StaticResource CardStyle}" Background="#E8F5E8">
                    <StackPanel>
                        <TextBlock Text="💰" FontSize="32" HorizontalAlignment="Center" Margin="0,0,0,10"/>
                        <TextBlock Text="{Binding TotalMonthlyIncome, StringFormat='{}{0:C}'}"
                                   FontSize="24"
                                   FontWeight="Bold"
                                   HorizontalAlignment="Center"
                                   Foreground="#4CAF50"/>
                        <TextBlock Text="Aylık Gelir"
                                   FontSize="14"
                                   HorizontalAlignment="Center"
                                   Foreground="#666666"/>
                    </StackPanel>
                </Border>

                <!-- Bekleyen Ödemeler -->
                <Border Grid.Column="3" Style="{StaticResource CardStyle}" Background="#FFF3E0">
                    <StackPanel>
                        <TextBlock Text="⏳" FontSize="32" HorizontalAlignment="Center" Margin="0,0,0,10"/>
                        <TextBlock Text="{Binding PendingPayments, StringFormat='{}{0:C}'}"
                                   FontSize="24"
                                   FontWeight="Bold"
                                   HorizontalAlignment="Center"
                                   Foreground="#FF9800"/>
                        <TextBlock Text="Bekleyen Ödeme"
                                   FontSize="14"
                                   HorizontalAlignment="Center"
                                   Foreground="#666666"/>
                    </StackPanel>
                </Border>
            </Grid>

            <!-- Hızlı İşlemler -->
            <Border Grid.Row="3" Style="{StaticResource CardStyle}" Margin="0,0,0,20">
                <StackPanel>
                    <TextBlock Text="Hızlı İşlemler" 
                               Style="{StaticResource SubtitleStyle}"/>
                    
                    <WrapPanel Orientation="Horizontal">
                        <Button Content="➕ Yeni Site Ekle" 
                                Style="{StaticResource PrimaryButton}" 
                                Margin="0,0,10,10"
                                Command="{Binding AddSiteCommand}"/>
                        
                        <Button Content="🏠 Yeni Apartman Ekle" 
                                Style="{StaticResource PrimaryButton}" 
                                Margin="0,0,10,10"
                                Command="{Binding AddApartmentCommand}"/>
                        
                        <Button Content="👤 Yeni Kiracı Ekle" 
                                Style="{StaticResource PrimaryButton}" 
                                Margin="0,0,10,10"
                                Command="{Binding AddTenantCommand}"/>
                        
                        <Button Content="💰 Ödeme Kaydet"
                                Style="{StaticResource SecondaryButton}"
                                Margin="0,0,10,10"
                                Command="{Binding AddPaymentCommand}"/>

                        <Button Content="👥 Kullanıcı Yönetimi"
                                Style="{StaticResource PrimaryButton}"
                                Margin="0,0,10,10"
                                Command="{Binding ManageUsersCommand}"
                                Visibility="{Binding CanShowUserManagement, Converter={StaticResource BooleanToVisibilityConverter}}"/>
                    </WrapPanel>
                </StackPanel>
            </Border>

            <!-- Son Ödemeler ve Yaklaşan Ödemeler -->
            <Grid Grid.Row="4" Margin="0,0,0,20">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>

                <!-- Son Ödemeler -->
                <Border Grid.Column="0" Style="{StaticResource CardStyle}" Margin="0,0,10,0">
                    <StackPanel>
                        <TextBlock Text="💰 Son Ödemeler"
                                   Style="{StaticResource SubtitleStyle}"/>

                        <ListView ItemsSource="{Binding RecentPayments}"
                                  MaxHeight="300"
                                  ScrollViewer.VerticalScrollBarVisibility="Auto">
                            <ListView.View>
                                <GridView>
                                    <GridViewColumn Header="Tarih" Width="80">
                                        <GridViewColumn.CellTemplate>
                                            <DataTemplate>
                                                <TextBlock Text="{Binding PaidDate, StringFormat='{}{0:dd/MM}'}"
                                                           FontSize="12"/>
                                            </DataTemplate>
                                        </GridViewColumn.CellTemplate>
                                    </GridViewColumn>
                                    <GridViewColumn Header="Tutar" Width="80">
                                        <GridViewColumn.CellTemplate>
                                            <DataTemplate>
                                                <TextBlock Text="{Binding Amount, StringFormat='{}{0:C}'}"
                                                           FontSize="12"
                                                           Foreground="#4CAF50"
                                                           FontWeight="SemiBold"/>
                                            </DataTemplate>
                                        </GridViewColumn.CellTemplate>
                                    </GridViewColumn>
                                    <GridViewColumn Header="Açıklama" Width="120">
                                        <GridViewColumn.CellTemplate>
                                            <DataTemplate>
                                                <TextBlock Text="{Binding Description}"
                                                           FontSize="12"
                                                           TextTrimming="CharacterEllipsis"/>
                                            </DataTemplate>
                                        </GridViewColumn.CellTemplate>
                                    </GridViewColumn>
                                </GridView>
                            </ListView.View>
                        </ListView>

                        <TextBlock Text="Henüz ödeme kaydı bulunmuyor."
                                   FontSize="14"
                                   Foreground="#999999"
                                   HorizontalAlignment="Center"
                                   Margin="0,20,0,0"
                                   Visibility="{Binding RecentPayments, Converter={StaticResource EmptyStateConverter}}"/>
                    </StackPanel>
                </Border>

                <!-- Yaklaşan Ödemeler -->
                <Border Grid.Column="1" Style="{StaticResource CardStyle}" Margin="10,0,0,0">
                    <StackPanel>
                        <TextBlock Text="⏰ Yaklaşan Ödemeler"
                                   Style="{StaticResource SubtitleStyle}"/>

                        <ListView ItemsSource="{Binding UpcomingPayments}"
                                  MaxHeight="300"
                                  ScrollViewer.VerticalScrollBarVisibility="Auto">
                            <ListView.View>
                                <GridView>
                                    <GridViewColumn Header="Vade" Width="80">
                                        <GridViewColumn.CellTemplate>
                                            <DataTemplate>
                                                <TextBlock Text="{Binding DueDate, StringFormat='{}{0:dd/MM}'}"
                                                           FontSize="12"/>
                                            </DataTemplate>
                                        </GridViewColumn.CellTemplate>
                                    </GridViewColumn>
                                    <GridViewColumn Header="Tutar" Width="80">
                                        <GridViewColumn.CellTemplate>
                                            <DataTemplate>
                                                <TextBlock Text="{Binding Amount, StringFormat='{}{0:C}'}"
                                                           FontSize="12"
                                                           Foreground="#FF9800"
                                                           FontWeight="SemiBold"/>
                                            </DataTemplate>
                                        </GridViewColumn.CellTemplate>
                                    </GridViewColumn>
                                    <GridViewColumn Header="Açıklama" Width="120">
                                        <GridViewColumn.CellTemplate>
                                            <DataTemplate>
                                                <TextBlock Text="{Binding Description}"
                                                           FontSize="12"
                                                           TextTrimming="CharacterEllipsis"/>
                                            </DataTemplate>
                                        </GridViewColumn.CellTemplate>
                                    </GridViewColumn>
                                </GridView>
                            </ListView.View>
                        </ListView>

                        <TextBlock Text="Yaklaşan ödeme bulunmuyor."
                                   FontSize="14"
                                   Foreground="#999999"
                                   HorizontalAlignment="Center"
                                   Margin="0,20,0,0"
                                   Visibility="{Binding UpcomingPayments, Converter={StaticResource EmptyStateConverter}}"/>
                    </StackPanel>
                </Border>
            </Grid>
        </Grid>
    </ScrollViewer>
</UserControl>
