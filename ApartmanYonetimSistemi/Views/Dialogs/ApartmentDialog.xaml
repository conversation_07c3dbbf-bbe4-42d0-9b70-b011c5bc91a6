<Window x:Class="ApartmanYonetimSistemi.Views.Dialogs.ApartmentDialog"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:converters="clr-namespace:ApartmanYonetimSistemi.Converters"
        Title="{Binding Title}" 
        Height="550" Width="500"
        WindowStartupLocation="CenterOwner"
        ResizeMode="NoResize"
        Background="#F5F5F5">

    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <Border Grid.Row="0" Style="{StaticResource CardStyle}" Margin="0,0,0,20">
            <StackPanel>
                <TextBlock Text="{Binding Title}" Style="{StaticResource TitleStyle}"/>
                <TextBlock Text="Apartman bilgilerini girin" 
                           Style="{StaticResource SubtitleStyle}" Opacity="0.7"/>
            </StackPanel>
        </Border>

        <!-- Form -->
        <Border Grid.Row="1" Style="{StaticResource CardStyle}" Margin="0,0,0,20">
            <ScrollViewer VerticalScrollBarVisibility="Auto">
                <StackPanel>
                    <!-- Apartment Name -->
                    <StackPanel Margin="0,0,0,15">
                        <TextBlock Text="Apartman Adı *" FontWeight="SemiBold" Margin="0,0,0,5"/>
                        <TextBox Text="{Binding ApartmentName, UpdateSourceTrigger=PropertyChanged}"
                                 Height="35" Padding="10,0"/>
                        <TextBlock Text="{Binding ApartmentNameError}" 
                                   Foreground="Red" FontSize="12" Margin="0,2,0,0"
                                   Visibility="{Binding ApartmentNameError, Converter={x:Static converters:StringToVisibilityConverter.Instance}}"/>
                    </StackPanel>

                    <!-- Site Selection -->
                    <StackPanel Margin="0,0,0,15">
                        <TextBlock Text="Site *" FontWeight="SemiBold" Margin="0,0,0,5"/>
                        <ComboBox ItemsSource="{Binding Sites}"
                                  SelectedValue="{Binding SelectedSiteId}"
                                  SelectedValuePath="Id"
                                  DisplayMemberPath="Name"
                                  Height="35"/>
                        <TextBlock Text="{Binding SiteError}" 
                                   Foreground="Red" FontSize="12" Margin="0,2,0,0"
                                   Visibility="{Binding SiteError, Converter={x:Static converters:StringToVisibilityConverter.Instance}}"/>
                    </StackPanel>

                    <!-- Floor Count and Unit Count -->
                    <Grid Margin="0,0,0,15">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="10"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>

                        <!-- Floor Count -->
                        <StackPanel Grid.Column="0">
                            <TextBlock Text="Kat Sayısı *" FontWeight="SemiBold" Margin="0,0,0,5"/>
                            <TextBox Text="{Binding FloorCount, UpdateSourceTrigger=PropertyChanged}"
                                     Height="35" Padding="10,0"/>
                        </StackPanel>

                        <!-- Unit Count -->
                        <StackPanel Grid.Column="2">
                            <TextBlock Text="Daire Sayısı *" FontWeight="SemiBold" Margin="0,0,0,5"/>
                            <TextBox Text="{Binding UnitCount, UpdateSourceTrigger=PropertyChanged}"
                                     Height="35" Padding="10,0"/>
                        </StackPanel>
                    </Grid>

                    <!-- Build Year -->
                    <StackPanel Margin="0,0,0,15">
                        <TextBlock Text="Yapım Yılı" FontWeight="SemiBold" Margin="0,0,0,5"/>
                        <TextBox Text="{Binding BuildYear, UpdateSourceTrigger=PropertyChanged}"
                                 Height="35" Padding="10,0" MaxLength="4"/>
                    </StackPanel>

                    <!-- Description -->
                    <StackPanel Margin="0,0,0,15">
                        <TextBlock Text="Açıklama" FontWeight="SemiBold" Margin="0,0,0,5"/>
                        <TextBox Text="{Binding Description, UpdateSourceTrigger=PropertyChanged}"
                                 Height="80" Padding="10,5"
                                 TextWrapping="Wrap"
                                 AcceptsReturn="True"
                                 VerticalScrollBarVisibility="Auto"/>
                    </StackPanel>
                </StackPanel>
            </ScrollViewer>
        </Border>

        <!-- Buttons -->
        <Border Grid.Row="2" Style="{StaticResource CardStyle}">
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Right">
                <Button Content="İptal" 
                        Style="{StaticResource SecondaryButton}"
                        Command="{Binding CancelCommand}"
                        Margin="0,0,10,0" 
                        Padding="20,10" 
                        MinWidth="80"/>
                <Button Content="{Binding SaveButtonText}" 
                        Style="{StaticResource PrimaryButton}"
                        Command="{Binding SaveCommand}"
                        Padding="20,10" 
                        MinWidth="80"/>
            </StackPanel>
        </Border>

        <!-- Loading Overlay -->
        <Border Grid.RowSpan="3" 
                Background="#80000000" 
                Visibility="{Binding IsBusy, Converter={x:Static converters:BooleanToVisibilityConverter.Instance}}">
            <StackPanel VerticalAlignment="Center" HorizontalAlignment="Center">
                <ProgressBar IsIndeterminate="True" Width="200" Height="4" Margin="0,0,0,10"/>
                <TextBlock Text="Kaydediliyor..." Foreground="White" FontSize="14" HorizontalAlignment="Center"/>
            </StackPanel>
        </Border>
    </Grid>
</Window>
