using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace ApartmanYonetimSistemi.Models
{
    public class Payment : BaseEntity
    {
        [Required]
        [StringLength(200)]
        public string Description { get; set; } = string.Empty;

        [Required]
        [Column(TypeName = "decimal(18,2)")]
        public decimal Amount { get; set; }

        public PaymentType Type { get; set; } = PaymentType.Rent;

        public PaymentStatus Status { get; set; } = PaymentStatus.Pending;

        public PaymentMethod Method { get; set; } = PaymentMethod.Cash;

        public DateTime DueDate { get; set; }

        public DateTime? PaidDate { get; set; }

        [StringLength(500)]
        public string? Notes { get; set; }

        // Foreign Keys
        [Required]
        [StringLength(50)]
        public string UnitId { get; set; } = string.Empty;

        [StringLength(50)]
        public string? TenantId { get; set; }

        [StringLength(50)]
        public string UserId { get; set; } = string.Empty; // Ödemeyi oluşturan kullanıcı

        // Navigation Properties
        [ForeignKey("UnitId")]
        public virtual Unit Unit { get; set; } = null!;

        [ForeignKey("TenantId")]
        public virtual Tenant? Tenant { get; set; }

        [ForeignKey("UserId")]
        public virtual User User { get; set; } = null!;
    }
}
