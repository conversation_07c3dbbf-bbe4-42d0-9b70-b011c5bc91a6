using ApartmanYonetimSistemi.Models;

namespace ApartmanYonetimSistemi.Services
{
    public interface IUnitService
    {
        Task<IEnumerable<Unit>> GetAllAsync();
        Task<Unit?> GetByIdAsync(string id);
        Task<IEnumerable<Unit>> GetByUserIdAsync(string userId);
        Task<IEnumerable<Unit>> GetByCompanyIdAsync(string? companyId);
        Task<IEnumerable<Unit>> GetByApartmentIdAsync(string apartmentId);
        Task<Unit> CreateAsync(Unit unit);
        Task<Unit> UpdateAsync(Unit unit);
        Task DeleteAsync(string id);
    }
}
