using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace ApartmanYonetimSistemi.Models
{
    public class Apartment : BaseEntity
    {
        [Required]
        [StringLength(200)]
        public string Name { get; set; } = string.Empty;

        [Required]
        [StringLength(50)]
        public string SiteId { get; set; } = string.Empty;

        [StringLength(10)]
        public string BlockNumber { get; set; } = string.Empty;

        public int TotalFloors { get; set; } = 0;

        public int UnitsPerFloor { get; set; } = 0;

        public int TotalUnits { get; set; } = 0;

        [StringLength(50)]
        public string CompanyId { get; set; } = string.Empty;

        [StringLength(50)]
        public string OwnerId { get; set; } = string.Empty;

        [Column(TypeName = "decimal(18,2)")]
        public decimal MonthlyMaintenanceFee { get; set; } = 0;

        [StringLength(1000)]
        public string Description { get; set; } = string.Empty;

        // Navigation properties
        public virtual Site Site { get; set; } = null!;
        public virtual ICollection<Unit> Units { get; set; } = new List<Unit>();
    }
}
