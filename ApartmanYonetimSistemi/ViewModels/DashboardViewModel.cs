using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using ApartmanYonetimSistemi.Services;
using ApartmanYonetimSistemi.Models;
using System.Threading.Tasks;
using System.Windows;

namespace ApartmanYonetimSistemi.ViewModels
{
    public partial class DashboardViewModel : BaseViewModel
    {
        private readonly ISiteService _siteService;
        private readonly IApartmentService _apartmentService;
        private readonly IUnitService _unitService;
        private readonly ITenantService _tenantService;
        private readonly IPaymentService _paymentService;

        [ObservableProperty]
        private int _totalSites = 0;

        [ObservableProperty]
        private int _totalApartments = 0;

        [ObservableProperty]
        private int _totalUnits = 0;

        [ObservableProperty]
        private int _activeTenants = 0;

        [ObservableProperty]
        private int _availableUnits = 0;

        [ObservableProperty]
        private int _occupiedUnits = 0;

        [ObservableProperty]
        private decimal _totalMonthlyIncome = 0;

        [ObservableProperty]
        private decimal _pendingPayments = 0;

        [ObservableProperty]
        private int _overduePayments = 0;

        [ObservableProperty]
        private List<Payment> _recentPayments = new();

        [ObservableProperty]
        private List<Payment> _upcomingPayments = new();

        [ObservableProperty]
        private bool _canShowUserManagement = false;

        [ObservableProperty]
        private string _welcomeMessage = string.Empty;

        private User? _currentUser;

        public DashboardViewModel(
            ISiteService siteService,
            IApartmentService apartmentService,
            IUnitService unitService,
            ITenantService tenantService,
            IPaymentService paymentService)
        {
            _siteService = siteService;
            _apartmentService = apartmentService;
            _unitService = unitService;
            _tenantService = tenantService;
            _paymentService = paymentService;

            Title = "Dashboard";
        }

        public void SetCurrentUser(User user)
        {
            _currentUser = user;
            CanShowUserManagement = user.Role == UserRole.Admin || user.Role == UserRole.SuperAdmin;

            // Hoş geldin mesajı
            var timeOfDay = DateTime.Now.Hour < 12 ? "Günaydın" :
                           DateTime.Now.Hour < 18 ? "İyi günler" : "İyi akşamlar";
            WelcomeMessage = $"{timeOfDay}, {user.FirstName} {user.LastName}!";

            // Dashboard verilerini yükle
            _ = LoadDashboardDataAsync();
        }

        private async Task LoadDashboardDataAsync()
        {
            try
            {
                IsBusy = true;

                if (_currentUser == null) return;

                // Kullanıcı rolüne göre veri yükleme
                if (_currentUser.Role == UserRole.SuperAdmin)
                {
                    // SuperAdmin tüm verileri görebilir
                    var sites = await _siteService.GetAllAsync();
                    TotalSites = sites.Count();

                    var apartments = await _apartmentService.GetAllAsync();
                    TotalApartments = apartments.Count();

                    var units = await _unitService.GetAllAsync();
                    TotalUnits = units.Count();

                    var tenants = await _tenantService.GetAllAsync();
                    ActiveTenants = tenants.Where(t => t.Status == TenantStatus.Active).Count();

                    // Daire durumları
                    AvailableUnits = units.Where(u => u.Status == UnitStatus.Available).Count();
                    OccupiedUnits = units.Where(u => u.Status == UnitStatus.Rented || u.Status == UnitStatus.OwnerOccupied).Count();

                    // Ödeme bilgileri
                    var payments = await _paymentService.GetAllAsync();
                    var currentMonth = DateTime.Now.Month;
                    var currentYear = DateTime.Now.Year;

                    TotalMonthlyIncome = payments
                        .Where(p => p.PaidDate.HasValue && p.PaidDate.Value.Month == currentMonth && p.PaidDate.Value.Year == currentYear && p.Status == PaymentStatus.Paid)
                        .Sum(p => p.Amount);

                    PendingPayments = payments
                        .Where(p => p.Status == PaymentStatus.Pending)
                        .Sum(p => p.Amount);

                    OverduePayments = payments
                        .Where(p => p.Status == PaymentStatus.Overdue)
                        .Count();

                    // Son ödemeler (son 10)
                    RecentPayments = payments
                        .Where(p => p.Status == PaymentStatus.Paid && p.PaidDate.HasValue)
                        .OrderByDescending(p => p.PaidDate.Value)
                        .Take(10)
                        .ToList();

                    // Yaklaşan ödemeler (gelecek 30 gün)
                    var thirtyDaysFromNow = DateTime.Now.AddDays(30);
                    UpcomingPayments = payments
                        .Where(p => p.Status == PaymentStatus.Pending && p.DueDate <= thirtyDaysFromNow)
                        .OrderBy(p => p.DueDate)
                        .Take(10)
                        .ToList();
                }
                else if (_currentUser.Role == UserRole.Admin)
                {
                    // Admin sadece kendi şirketinin verilerini görebilir
                    var sites = await _siteService.GetByCompanyIdAsync(_currentUser.CompanyId);
                    TotalSites = sites.Count();

                    var apartments = await _apartmentService.GetByCompanyIdAsync(_currentUser.CompanyId);
                    TotalApartments = apartments.Count();

                    var units = await _unitService.GetByCompanyIdAsync(_currentUser.CompanyId);
                    TotalUnits = units.Count();

                    var tenants = await _tenantService.GetByCompanyIdAsync(_currentUser.CompanyId);
                    ActiveTenants = tenants.Where(t => t.Status == TenantStatus.Active).Count();
                }
                else
                {
                    // Normal kullanıcı ve Emlakçı sadece kendi verilerini görebilir
                    var sites = await _siteService.GetByUserIdAsync(_currentUser.Id);
                    TotalSites = sites.Count();

                    var apartments = await _apartmentService.GetByUserIdAsync(_currentUser.Id);
                    TotalApartments = apartments.Count();

                    var units = await _unitService.GetByUserIdAsync(_currentUser.Id);
                    TotalUnits = units.Count();

                    var tenants = await _tenantService.GetByUserIdAsync(_currentUser.Id);
                    ActiveTenants = tenants.Where(t => t.Status == TenantStatus.Active).Count();
                }
            }
            catch (Exception ex)
            {
                // Hata durumunda varsayılan değerler
                TotalSites = 0;
                TotalApartments = 0;
                TotalUnits = 0;
                ActiveTenants = 0;
            }
            finally
            {
                IsBusy = false;
            }
        }

        [RelayCommand]
        private void AddSite()
        {
            MessageBox.Show("Site ekleme özelliği yakında eklenecek!", "Bilgi", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        [RelayCommand]
        private void AddApartment()
        {
            MessageBox.Show("Apartman ekleme özelliği yakında eklenecek!", "Bilgi", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        [RelayCommand]
        private void AddTenant()
        {
            MessageBox.Show("Kiracı ekleme özelliği yakında eklenecek!", "Bilgi", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        [RelayCommand]
        private void AddPayment()
        {
            MessageBox.Show("Ödeme ekleme özelliği yakında eklenecek!", "Bilgi", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        [RelayCommand]
        private void ManageUsers()
        {
            MessageBox.Show("Kullanıcı yönetimi özelliği yakında eklenecek!", "Bilgi", MessageBoxButton.OK, MessageBoxImage.Information);
        }
    }
}
