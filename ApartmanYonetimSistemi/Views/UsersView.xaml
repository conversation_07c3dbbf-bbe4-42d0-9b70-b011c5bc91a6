<UserControl x:Class="ApartmanYonetimSistemi.Views.UsersView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:converters="clr-namespace:ApartmanYonetimSistemi.Converters">

    <UserControl.Resources>
        <!-- Role Color Converter -->
        <Style x:Key="RoleIndicator" TargetType="Border">
            <Setter Property="CornerRadius" Value="10"/>
            <Setter Property="Padding" Value="8,2"/>
            <Setter Property="HorizontalAlignment" Value="Center"/>
            <Style.Triggers>
                <DataTrigger Binding="{Binding Role}" Value="SuperAdmin">
                    <Setter Property="Background" Value="#E91E63"/>
                </DataTrigger>
                <DataTrigger Binding="{Binding Role}" Value="Admin">
                    <Setter Property="Background" Value="#FF9800"/>
                </DataTrigger>
                <DataTrigger Binding="{Binding Role}" Value="Realtor">
                    <Setter Property="Background" Value="#2196F3"/>
                </DataTrigger>
                <DataTrigger Binding="{Binding Role}" Value="User">
                    <Setter Property="Background" Value="#4CAF50"/>
                </DataTrigger>
            </Style.Triggers>
        </Style>
    </UserControl.Resources>

    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <Border Grid.Row="0" Style="{StaticResource CardStyle}" Margin="0,0,0,10">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <StackPanel Grid.Column="0">
                    <TextBlock Text="👨‍💼 Kullanıcı Yönetimi" Style="{StaticResource TitleStyle}"/>
                    <TextBlock Text="Kullanıcıları görüntüleyin, ekleyin ve yönetin" 
                               Style="{StaticResource SubtitleStyle}" Opacity="0.7"/>
                </StackPanel>

                <StackPanel Grid.Column="1" Orientation="Horizontal">
                    <Button Content="➕ Yeni Kullanıcı" 
                            Style="{StaticResource PrimaryButton}"
                            Command="{Binding AddUserCommand}"
                            Margin="0,0,10,0"/>
                    <Button Content="🔄 Yenile" 
                            Style="{StaticResource SecondaryButton}"
                            Command="{Binding RefreshDataCommand}"/>
                </StackPanel>
            </Grid>
        </Border>

        <!-- Filters -->
        <Border Grid.Row="1" Style="{StaticResource CardStyle}" Margin="0,0,0,10">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="2*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>

                <TextBlock Grid.Row="0" Grid.ColumnSpan="4" Text="Filtreler" 
                           Style="{StaticResource SubtitleStyle}" Margin="0,0,0,10"/>

                <!-- Search -->
                <StackPanel Grid.Row="1" Grid.Column="0" Margin="0,0,10,0">
                    <TextBlock Text="Arama" FontWeight="SemiBold" FontSize="12" Foreground="#666" Margin="0,0,0,2"/>
                    <TextBox Text="{Binding SearchText, UpdateSourceTrigger=PropertyChanged}"
                             Height="32" Padding="8,0"
                             ToolTip="Ad, soyad veya email..."/>
                </StackPanel>

                <!-- Role Filter -->
                <StackPanel Grid.Row="1" Grid.Column="1" Margin="0,0,10,0">
                    <TextBlock Text="Rol" FontWeight="SemiBold" FontSize="12" Foreground="#666" Margin="0,0,0,2"/>
                    <ComboBox SelectedValue="{Binding SelectedRole}" Height="32">
                        <ComboBoxItem Content="Tümü" Tag="{x:Null}"/>
                        <ComboBoxItem Content="Süper Admin" Tag="SuperAdmin"/>
                        <ComboBoxItem Content="Admin" Tag="Admin"/>
                        <ComboBoxItem Content="Emlakçı" Tag="Realtor"/>
                        <ComboBoxItem Content="Kullanıcı" Tag="User"/>
                    </ComboBox>
                </StackPanel>

                <!-- Company Filter -->
                <StackPanel Grid.Row="1" Grid.Column="2" Margin="0,0,10,0">
                    <TextBlock Text="Şirket" FontWeight="SemiBold" FontSize="12" Foreground="#666" Margin="0,0,0,2"/>
                    <ComboBox SelectedValue="{Binding SelectedCompanyId}"
                              SelectedValuePath="Id"
                              ItemsSource="{Binding Companies}"
                              DisplayMemberPath="Name"
                              Height="32"/>
                </StackPanel>

                <!-- Clear Filters -->
                <StackPanel Grid.Row="1" Grid.Column="3" VerticalAlignment="Bottom">
                    <Button Content="Temizle" 
                            Style="{StaticResource SecondaryButton}"
                            Command="{Binding ClearFiltersCommand}"
                            Padding="15,8"/>
                </StackPanel>
            </Grid>
        </Border>

        <!-- Users List -->
        <Border Grid.Row="2" Style="{StaticResource CardStyle}">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                </Grid.RowDefinitions>

                <!-- List Header -->
                <TextBlock Grid.Row="0" Text="Kullanıcılar" Style="{StaticResource SubtitleStyle}" Margin="0,0,0,10"/>

                <!-- Loading Indicator -->
                <ProgressBar Grid.Row="1" IsIndeterminate="True" 
                             Visibility="{Binding IsBusy, Converter={x:Static converters:BooleanToVisibilityConverter.Instance}}"
                             Height="4" VerticalAlignment="Top"/>

                <!-- Data Grid -->
                <DataGrid Grid.Row="1" 
                          ItemsSource="{Binding FilteredUsers}"
                          SelectedItem="{Binding SelectedUser}"
                          AutoGenerateColumns="False"
                          CanUserAddRows="False"
                          CanUserDeleteRows="False"
                          IsReadOnly="True"
                          GridLinesVisibility="Horizontal"
                          HeadersVisibility="Column"
                          SelectionMode="Single"
                          AlternatingRowBackground="#F9F9F9">

                    <DataGrid.Columns>
                        <!-- Full Name -->
                        <DataGridTextColumn Header="Ad Soyad" Width="150">
                            <DataGridTextColumn.Binding>
                                <Binding Path="FullName"/>
                            </DataGridTextColumn.Binding>
                        </DataGridTextColumn>

                        <!-- Email -->
                        <DataGridTextColumn Header="Email" Binding="{Binding Email}" Width="200"/>

                        <!-- Role -->
                        <DataGridTemplateColumn Header="Rol" Width="120">
                            <DataGridTemplateColumn.CellTemplate>
                                <DataTemplate>
                                    <Border Style="{StaticResource RoleIndicator}">
                                        <TextBlock Text="{Binding Role}" Foreground="White" FontWeight="SemiBold" FontSize="11"/>
                                    </Border>
                                </DataTemplate>
                            </DataGridTemplateColumn.CellTemplate>
                        </DataGridTemplateColumn>

                        <!-- Company -->
                        <DataGridTextColumn Header="Şirket" Width="150">
                            <DataGridTextColumn.Binding>
                                <Binding Path="Company.Name"/>
                            </DataGridTextColumn.Binding>
                        </DataGridTextColumn>

                        <!-- Created Date -->
                        <DataGridTextColumn Header="Kayıt Tarihi" Width="100">
                            <DataGridTextColumn.Binding>
                                <Binding Path="CreatedAt" StringFormat="dd.MM.yyyy"/>
                            </DataGridTextColumn.Binding>
                        </DataGridTextColumn>

                        <!-- Status -->
                        <DataGridTemplateColumn Header="Durum" Width="80">
                            <DataGridTemplateColumn.CellTemplate>
                                <DataTemplate>
                                    <Border Background="{Binding IsActive, Converter={x:Static converters:BooleanToColorConverter.Instance}}"
                                            CornerRadius="10" Padding="8,2" HorizontalAlignment="Center">
                                        <TextBlock Text="{Binding IsActive, Converter={x:Static converters:BooleanToStatusConverter.Instance}}" 
                                                   Foreground="White" FontWeight="SemiBold" FontSize="11"/>
                                    </Border>
                                </DataTemplate>
                            </DataGridTemplateColumn.CellTemplate>
                        </DataGridTemplateColumn>

                        <!-- Actions -->
                        <DataGridTemplateColumn Header="İşlemler" Width="200">
                            <DataGridTemplateColumn.CellTemplate>
                                <DataTemplate>
                                    <StackPanel Orientation="Horizontal">
                                        <Button Content="👁️" 
                                                Command="{Binding DataContext.ViewUserDetailsCommand, RelativeSource={RelativeSource AncestorType=UserControl}}"
                                                CommandParameter="{Binding}"
                                                Style="{StaticResource SecondaryButton}"
                                                Padding="8,4" Margin="0,0,5,0"
                                                ToolTip="Detayları Görüntüle"/>
                                        <Button Content="✏️" 
                                                Command="{Binding DataContext.EditUserCommand, RelativeSource={RelativeSource AncestorType=UserControl}}"
                                                CommandParameter="{Binding}"
                                                Style="{StaticResource SecondaryButton}"
                                                Padding="8,4" Margin="0,0,5,0"
                                                ToolTip="Düzenle"/>
                                        <Button Content="🔒" 
                                                Command="{Binding DataContext.ToggleUserStatusCommand, RelativeSource={RelativeSource AncestorType=UserControl}}"
                                                CommandParameter="{Binding}"
                                                Style="{StaticResource SecondaryButton}"
                                                Padding="8,4"
                                                ToolTip="Aktif/Pasif"/>
                                    </StackPanel>
                                </DataTemplate>
                            </DataGridTemplateColumn.CellTemplate>
                        </DataGridTemplateColumn>
                    </DataGrid.Columns>
                </DataGrid>

                <!-- Empty State -->
                <StackPanel Grid.Row="1"
                            Visibility="{Binding FilteredUsers.Count, Converter={x:Static converters:EmptyStateConverter.Instance}}"
                            VerticalAlignment="Center" HorizontalAlignment="Center">
                    <TextBlock Text="👨‍💼" FontSize="48" HorizontalAlignment="Center" Opacity="0.3"/>
                    <TextBlock Text="Henüz kullanıcı eklenmemiş" 
                               FontSize="16" HorizontalAlignment="Center" 
                               Margin="0,10,0,5" Opacity="0.6"/>
                    <TextBlock Text="İlk kullanıcıyı eklemek için 'Yeni Kullanıcı' butonuna tıklayın" 
                               FontSize="12" HorizontalAlignment="Center" 
                               Opacity="0.5"/>
                </StackPanel>
            </Grid>
        </Border>
    </Grid>
</UserControl>
