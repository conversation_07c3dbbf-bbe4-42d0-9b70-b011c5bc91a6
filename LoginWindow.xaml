<Window x:Class="ApartmanYonetimSistemi.Views.LoginView"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="Apartman Yönetim <PERSON>" Height="600" Width="800"
        WindowStartupLocation="CenterScreen"
        ResizeMode="CanResize"
        Background="#F5F5F5"
        Loaded="Window_Loaded">

    <Window.Resources>
        <!-- Modern Button Style -->
        <Style x:Key="ModernButton" TargetType="Button">
            <Setter Property="Background" Value="#2196F3"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Padding" Value="20,12"/>
            <Setter Property="FontSize" Value="16"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}"
                                CornerRadius="5"
                                BorderThickness="{TemplateBinding BorderThickness}"
                                BorderBrush="{TemplateBinding BorderBrush}">
                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="#1976D2"/>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter Property="Background" Value="#0D47A1"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- Link Button Style -->
        <Style x:Key="LinkButton" TargetType="Button">
            <Setter Property="Background" Value="Transparent"/>
            <Setter Property="Foreground" Value="#2196F3"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Padding" Value="5"/>
            <Setter Property="FontSize" Value="12"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}">
                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Foreground" Value="#1976D2"/>
                                <Setter Property="TextBlock.TextDecorations" Value="Underline"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- Modern TextBox Style -->
        <Style x:Key="ModernTextBox" TargetType="TextBox">
            <Setter Property="Padding" Value="10,6"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="BorderBrush" Value="#CCCCCC"/>
            <Setter Property="Background" Value="White"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="TextBox">
                        <Border Background="{TemplateBinding Background}"
                                BorderBrush="{TemplateBinding BorderBrush}"
                                BorderThickness="{TemplateBinding BorderThickness}"
                                CornerRadius="5">
                            <ScrollViewer x:Name="PART_ContentHost"
                                        Margin="{TemplateBinding Padding}"
                                        VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsFocused" Value="True">
                                <Setter Property="BorderBrush" Value="#2196F3"/>
                                <Setter Property="BorderThickness" Value="2"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- Modern PasswordBox Style -->
        <Style x:Key="ModernPasswordBox" TargetType="PasswordBox">
            <Setter Property="Padding" Value="10,6"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="BorderBrush" Value="#CCCCCC"/>
            <Setter Property="Background" Value="White"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="PasswordBox">
                        <Border Background="{TemplateBinding Background}"
                                BorderBrush="{TemplateBinding BorderBrush}"
                                BorderThickness="{TemplateBinding BorderThickness}"
                                CornerRadius="5">
                            <ScrollViewer x:Name="PART_ContentHost"
                                        Margin="{TemplateBinding Padding}"
                                        VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsFocused" Value="True">
                                <Setter Property="BorderBrush" Value="#2196F3"/>
                                <Setter Property="BorderThickness" Value="2"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <Style x:Key="RoundedButton" TargetType="Button">
            <Setter Property="Background" Value="#2196F3"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="FontSize" Value="16"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="Padding" Value="0,12"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border x:Name="border"
                                Background="{TemplateBinding Background}"
                                CornerRadius="8"
                                BorderThickness="{TemplateBinding BorderThickness}"
                                BorderBrush="{TemplateBinding BorderBrush}">
                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter TargetName="border" Property="Background" Value="#1976D2"/>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter TargetName="border" Property="Background" Value="#0D47A1"/>
                                <Setter TargetName="border" Property="BorderThickness" Value="2"/>
                                <Setter TargetName="border" Property="BorderBrush" Value="#90CAF9"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
    </Window.Resources>

    <Viewbox Stretch="Uniform">
        <Grid>
            <!-- Background Gradient -->
            <Grid.Background>
                <LinearGradientBrush StartPoint="0,0" EndPoint="1,1">
                    <GradientStop Color="#E3F2FD" Offset="0"/>
                    <GradientStop Color="#BBDEFB" Offset="1"/>
                </LinearGradientBrush>
            </Grid.Background>

            <!-- Main Content -->
            <Border Background="White"
                    CornerRadius="15"
                    Margin="50"
                    Padding="40">
                <Border.Effect>
                    <DropShadowEffect Color="Gray" Direction="315" ShadowDepth="5" Opacity="0.3" BlurRadius="10"/>
                </Border.Effect>

                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>

                    <!-- Logo/Icon Area -->
                    <StackPanel Grid.Row="0" HorizontalAlignment="Center" Margin="0,0,0,20">
                        <Ellipse Width="80" Height="80" Fill="#2196F3" Margin="0,0,0,15">
                            <Ellipse.Effect>
                                <DropShadowEffect Color="Gray" Direction="315" ShadowDepth="3" Opacity="0.3" BlurRadius="8"/>
                            </Ellipse.Effect>
                        </Ellipse>
                        <Path Data="M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2M12,4A8,8 0 0,1 20,12A8,8 0 0,1 12,20A8,8 0 0,1 4,12A8,8 0 0,1 12,4M12,6A6,6 0 0,0 6,12A6,6 0 0,0 12,18A6,6 0 0,0 18,12A6,6 0 0,0 12,6M12,8A4,4 0 0,1 16,12A4,4 0 0,1 12,16A4,4 0 0,1 8,12A4,4 0 0,1 12,8Z"
                              Fill="White"
                              Width="40"
                              Height="40"
                              Stretch="Uniform"
                              HorizontalAlignment="Center"
                              VerticalAlignment="Center"
                              Margin="0,-60,0,0"/>
                    </StackPanel>

                    <!-- Başlık -->
                    <StackPanel Grid.Row="1" HorizontalAlignment="Center" Margin="0,0,0,30">
                        <TextBlock Text="Apartman Yönetim Sistemi"
                                   FontSize="28"
                                   FontWeight="Bold"
                                   Foreground="#1976D2"
                                   HorizontalAlignment="Center"
                                   Margin="0,0,0,5"/>
                        <TextBlock Text="Güvenli Giriş"
                                   FontSize="16"
                                   Foreground="#666666"
                                   HorizontalAlignment="Center"/>
                    </StackPanel>

                    <!-- Giriş Formu -->
                    <StackPanel Grid.Row="2" VerticalAlignment="Center" MaxWidth="350">
                        <!-- E-posta -->
                        <TextBlock Text="E-posta Adresi"
                                   FontSize="14"
                                   FontWeight="SemiBold"
                                   Foreground="#333333"
                                   Margin="0,0,0,8"/>
                        <TextBox x:Name="EmailTextBox"
                                 Text="{Binding Email, UpdateSourceTrigger=PropertyChanged}"
                                 Style="{StaticResource ModernTextBox}"
                                 Margin="0,0,0,15" FontSize="13" Padding="8,6"
                                 KeyDown="EmailTextBox_KeyDown"/>

                        <!-- Şifre -->
                        <TextBlock Text="Şifre"
                                   FontSize="14"
                                   FontWeight="SemiBold"
                                   Foreground="#333333"
                                   Margin="0,0,0,8"/>
                        <PasswordBox x:Name="PasswordBox"
                                     Style="{StaticResource ModernPasswordBox}"
                                     KeyDown="PasswordBox_KeyDown"
                                     Margin="0,0,0,15" FontSize="13" Padding="8,6"/>

                        <!-- Giriş Butonu -->
                        <Button Content="GİRİŞ YAP"
                                Style="{StaticResource RoundedButton}"
                                Command="{Binding LoginCommand}"
                                CommandParameter="{Binding ElementName=PasswordBox}"
                                Margin="0,10,0,0" FontSize="17" Height="48"/>

                        <!-- Link Butonları -->
                        <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                            <Button Content="Şifremi Unuttum"
                                    Command="{Binding ForgotPasswordCommand}"
                                    Style="{StaticResource LinkButton}"
                                    Margin="0,0,20,0"/>
                            <TextBlock Text="|"
                                       Foreground="#CCCCCC"
                                       VerticalAlignment="Center"
                                       Margin="0,0,20,0"/>
                            <Button Content="Kayıt Ol"
                                    Command="{Binding RegisterCommand}"
                                    Style="{StaticResource LinkButton}"/>
                        </StackPanel>
                    </StackPanel>

                    <!-- Durum Mesajı -->
                    <TextBlock Grid.Row="3"
                               Text="{Binding StatusMessage}"
                               Foreground="{Binding StatusColor}"
                               FontSize="12"
                               FontWeight="SemiBold"
                               HorizontalAlignment="Center"
                               TextAlignment="Center"
                               TextWrapping="Wrap"
                               Margin="0,20,0,0"/>
                </Grid>
            </Border>
        </Grid>
    </Viewbox>
</Window>