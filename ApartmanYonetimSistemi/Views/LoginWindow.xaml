<Window x:Class="ApartmanYonetimSistemi.Views.LoginWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:converters="clr-namespace:ApartmanYonetimSistemi.Converters"
        mc:Ignorable="d"
        Title="Apartman Yönetim Sistemi - Giriş"
        Height="750" Width="550"
        WindowStartupLocation="CenterScreen"
        ResizeMode="NoResize"
        Background="#F5F5F5">
    
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="220"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <Border Grid.Row="0" Background="Linear Gradient(45deg, #2196F3, #21CBF3)">
            <StackPanel VerticalAlignment="Center" HorizontalAlignment="Center">
                <TextBlock Text="🏢" FontSize="48" HorizontalAlignment="Center" Margin="0,0,0,10"/>
                <TextBlock Text="Apartman Yönetim Sistemi" 
                           FontSize="24" FontWeight="Bold" 
                           Foreground="White" HorizontalAlignment="Center"/>
                <TextBlock Text="Hoş Geldiniz" 
                           FontSize="14" Foreground="White" 
                           HorizontalAlignment="Center" Opacity="0.9" Margin="0,5,0,0"/>
            </StackPanel>
        </Border>

        <!-- Main Content -->
        <Border Grid.Row="1" Background="White" Margin="40,30,40,40" 
                CornerRadius="10" Effect="{StaticResource CardShadow}">
            <ScrollViewer VerticalScrollBarVisibility="Auto" Padding="40">
                <StackPanel>
                    <!-- Login Title -->
                    <TextBlock Text="Giriş Yap" FontSize="28" FontWeight="Bold" 
                               HorizontalAlignment="Center" Margin="0,0,0,30"/>

                    <!-- Email -->
                    <StackPanel Margin="0,0,0,20">
                        <TextBlock Text="Email:" FontWeight="SemiBold" Margin="0,0,0,8" FontSize="14"/>
                        <TextBox x:Name="EmailTextBox" 
                                 Text="{Binding Email, UpdateSourceTrigger=PropertyChanged}"
                                 Height="45" Padding="15,0" FontSize="14"
                                 BorderBrush="#DDD" BorderThickness="2"
                                 VerticalContentAlignment="Center"/>
                    </StackPanel>

                    <!-- Password -->
                    <StackPanel Margin="0,0,0,25">
                        <TextBlock Text="Şifre:" FontWeight="SemiBold" Margin="0,0,0,8" FontSize="14"/>
                        <PasswordBox x:Name="PasswordBox" 
                                     Height="45" Padding="15,0" FontSize="14"
                                     BorderBrush="#DDD" BorderThickness="2"
                                     VerticalContentAlignment="Center"
                                     PasswordChanged="PasswordBox_PasswordChanged"/>
                    </StackPanel>

                    <!-- Error Message -->
                    <TextBlock Text="{Binding ErrorMessage}" 
                               Foreground="Red" FontSize="12" 
                               HorizontalAlignment="Center" Margin="0,0,0,15"
                               Visibility="{Binding ErrorMessage, Converter={x:Static converters:StringToVisibilityConverter.Instance}}"/>

                    <!-- Login Button -->
                    <Button Content="🔑 Giriş Yap" 
                            Command="{Binding LoginCommand}"
                            Height="50" FontSize="16" FontWeight="SemiBold"
                            Background="#2196F3" Foreground="White"
                            BorderThickness="0" Margin="0,0,0,20"
                            Cursor="Hand">
                        <Button.Style>
                            <Style TargetType="Button">
                                <Setter Property="Template">
                                    <Setter.Value>
                                        <ControlTemplate TargetType="Button">
                                            <Border Background="{TemplateBinding Background}" 
                                                    CornerRadius="8" Padding="{TemplateBinding Padding}">
                                                <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                            </Border>
                                            <ControlTemplate.Triggers>
                                                <Trigger Property="IsMouseOver" Value="True">
                                                    <Setter Property="Background" Value="#1976D2"/>
                                                </Trigger>
                                                <Trigger Property="IsPressed" Value="True">
                                                    <Setter Property="Background" Value="#1565C0"/>
                                                </Trigger>
                                            </ControlTemplate.Triggers>
                                        </ControlTemplate>
                                    </Setter.Value>
                                </Setter>
                            </Style>
                        </Button.Style>
                    </Button>

                    <!-- Register Button -->
                    <Button Content="📝 Yeni Hesap Oluştur" 
                            Click="RegisterLink_Click"
                            Height="50" FontSize="16" FontWeight="SemiBold"
                            Background="#4CAF50" Foreground="White"
                            BorderThickness="0" Margin="0,0,0,15"
                            Cursor="Hand">
                        <Button.Style>
                            <Style TargetType="Button">
                                <Setter Property="Template">
                                    <Setter.Value>
                                        <ControlTemplate TargetType="Button">
                                            <Border Background="{TemplateBinding Background}" 
                                                    CornerRadius="8" Padding="{TemplateBinding Padding}">
                                                <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                            </Border>
                                            <ControlTemplate.Triggers>
                                                <Trigger Property="IsMouseOver" Value="True">
                                                    <Setter Property="Background" Value="#45a049"/>
                                                </Trigger>
                                                <Trigger Property="IsPressed" Value="True">
                                                    <Setter Property="Background" Value="#3d8b40"/>
                                                </Trigger>
                                            </ControlTemplate.Triggers>
                                        </ControlTemplate>
                                    </Setter.Value>
                                </Setter>
                            </Style>
                        </Button.Style>
                    </Button>

                    <!-- Forgot Password Link -->
                    <StackPanel HorizontalAlignment="Center" Margin="0,10,0,0">
                        <Button Content="🔑 Şifremi Unuttum" 
                                Command="{Binding ForgotPasswordCommand}"
                                Style="{StaticResource LinkButton}"
                                Margin="0,0,20,0"
                                Click="ForgotPassword_Click"/>
                    </StackPanel>

                    <!-- Loading Indicator -->
                    <StackPanel Visibility="{Binding IsBusy, Converter={x:Static converters:BooleanToVisibilityConverter.Instance}}"
                                HorizontalAlignment="Center" Margin="0,20,0,0">
                        <ProgressBar IsIndeterminate="True" Width="200" Height="4" Margin="0,0,0,10"/>
                        <TextBlock Text="Giriş yapılıyor..." FontSize="12" HorizontalAlignment="Center" Foreground="#666"/>
                    </StackPanel>

                    <!-- Status Message -->
                    <TextBlock Text="{Binding StatusMessage}"
                               Foreground="{Binding StatusColor}"
                               FontSize="12"
                               FontWeight="SemiBold"
                               HorizontalAlignment="Center"
                               TextAlignment="Center"
                               TextWrapping="Wrap"
                               Margin="0,20,0,0"/>
                </StackPanel>
            </ScrollViewer>
        </Border>

        <!-- Footer -->
        <TextBlock Grid.Row="1" Text="© 2025 Apartman Yönetim Sistemi" 
                   HorizontalAlignment="Center" VerticalAlignment="Bottom"
                   FontSize="12" Foreground="#666" Margin="0,0,0,20"/>
    </Grid>
</Window>
