using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace ApartmanYonetimSistemi.Models
{
    public class User : BaseEntity
    {
        [Required]
        [StringLength(255)]
        [EmailAddress]
        public string Email { get; set; } = string.Empty;

        [StringLength(100)]
        public string FirstName { get; set; } = string.Empty;

        [StringLength(100)]
        public string LastName { get; set; } = string.Empty;

        [StringLength(20)]
        public string PhoneNumber { get; set; } = string.Empty;

        [Required]
        public UserRole Role { get; set; } = UserRole.User;

        [Required]
        public UserStatus Status { get; set; } = UserStatus.Active;

        [StringLength(50)]
        public string? CompanyId { get; set; } = null; // Emlak şirketi ID'si (Admin ve Emlakçılar için)

        [StringLength(200)]
        public string? CompanyName { get; set; } = null; // Emlak şirketi adı

        // Navigation Properties
        public Company? Company { get; set; }

        [StringLength(255)]
        public string PasswordHash { get; set; } = string.Empty;

        public DateTime? LastLoginAt { get; set; }

        public bool IsEmailVerified { get; set; } = false;
        public bool IsActive { get; set; } = true;

        [NotMapped]
        public string FullName => $"{FirstName} {LastName}".Trim();
    }
}
