using System.Collections.ObjectModel;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using ApartmanYonetimSistemi.Models;
using ApartmanYonetimSistemi.Services;
using System.Threading.Tasks;
using System.Windows;
using System.Linq;

namespace ApartmanYonetimSistemi.ViewModels
{
    public partial class PaymentsViewModel : BaseViewModel
    {
        private readonly IPaymentService _paymentService;

        [ObservableProperty]
        private ObservableCollection<Payment> _payments = new();

        [ObservableProperty]
        private ObservableCollection<Payment> _filteredPayments = new();

        [ObservableProperty]
        private PaymentType? _selectedPaymentType;

        [ObservableProperty]
        private PaymentStatus? _selectedStatus;

        [ObservableProperty]
        private string _selectedDateRange = "All";

        [ObservableProperty]
        private Payment? _selectedPayment;

        [ObservableProperty]
        private decimal _totalAmount;

        private User? _currentUser;

        partial void OnSelectedPaymentTypeChanged(PaymentType? value) => ApplyFilter();
        partial void OnSelectedStatusChanged(PaymentStatus? value) => ApplyFilter();
        partial void OnSelectedDateRangeChanged(string value) => ApplyFilter();

        public PaymentsViewModel(IPaymentService paymentService)
        {
            _paymentService = paymentService;
            Title = "Ödeme Yönetimi";
        }

        public void SetCurrentUser(User user)
        {
            _currentUser = user;
            _ = LoadDataAsync();
        }

        public async Task LoadDataAsync()
        {
            try
            {
                IsBusy = true;
                Payments.Clear();

                if (_currentUser == null) return;

                IEnumerable<Payment> paymentList;

                if (_currentUser.Role == UserRole.SuperAdmin)
                {
                    paymentList = await _paymentService.GetAllAsync();
                }
                else if (_currentUser.Role == UserRole.Admin)
                {
                    paymentList = await _paymentService.GetByCompanyIdAsync(_currentUser.CompanyId);
                }
                else
                {
                    paymentList = await _paymentService.GetByUserIdAsync(_currentUser.Id);
                }

                foreach (var payment in paymentList.OrderByDescending(p => p.DueDate))
                {
                    Payments.Add(payment);
                }

                ApplyFilter();
            }
            catch (Exception ex)
            {
                Payments.Clear();
                FilteredPayments.Clear();
            }
            finally
            {
                IsBusy = false;
            }
        }

        private void ApplyFilter()
        {
            var query = Payments.AsEnumerable();

            if (SelectedPaymentType.HasValue)
                query = query.Where(p => p.Type == SelectedPaymentType.Value);

            if (SelectedStatus.HasValue)
                query = query.Where(p => p.Status == SelectedStatus.Value);

            // Date range filter
            var now = DateTime.Now;
            switch (SelectedDateRange)
            {
                case "ThisMonth":
                    query = query.Where(p => p.DueDate.Month == now.Month && p.DueDate.Year == now.Year);
                    break;
                case "Last3Months":
                    var threeMonthsAgo = now.AddMonths(-3);
                    query = query.Where(p => p.DueDate >= threeMonthsAgo);
                    break;
                case "ThisYear":
                    query = query.Where(p => p.DueDate.Year == now.Year);
                    break;
            }

            var filtered = new ObservableCollection<Payment>();
            decimal total = 0;

            foreach (var payment in query.OrderByDescending(p => p.DueDate))
            {
                filtered.Add(payment);
                total += payment.Amount;
            }

            FilteredPayments = filtered;
            TotalAmount = total;
        }

        [RelayCommand]
        private async Task AddPayment()
        {
            if (_currentUser == null) return;

            try
            {
                var paymentService = Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetRequiredService<IPaymentService>(((App)App.Current).Services);
                var unitService = Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetRequiredService<IUnitService>(((App)App.Current).Services);
                var tenantService = Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetRequiredService<ITenantService>(((App)App.Current).Services);

                var viewModel = new ViewModels.Dialogs.PaymentDialogViewModel(paymentService, unitService, tenantService, _currentUser);
                var dialog = new Views.Dialogs.PaymentDialog { DataContext = viewModel };

                if (Application.Current.MainWindow != null)
                {
                    dialog.Owner = Application.Current.MainWindow;
                }

                var result = dialog.ShowDialog();

                if (result == true && viewModel.DialogResult)
                {
                    await LoadDataAsync();
                    MessageBox.Show("Ödeme başarıyla eklendi.", "Başarılı", MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Ödeme ekleme hatası: {ex.Message}", "Hata", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        [RelayCommand]
        private async Task EditPayment(Payment? payment)
        {
            if (payment == null || _currentUser == null) return;

            try
            {
                var paymentService = Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetRequiredService<IPaymentService>(((App)App.Current).Services);
                var unitService = Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetRequiredService<IUnitService>(((App)App.Current).Services);
                var tenantService = Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetRequiredService<ITenantService>(((App)App.Current).Services);

                var viewModel = new ViewModels.Dialogs.PaymentDialogViewModel(paymentService, unitService, tenantService, _currentUser, payment);
                var dialog = new Views.Dialogs.PaymentDialog { DataContext = viewModel };

                if (Application.Current.MainWindow != null)
                {
                    dialog.Owner = Application.Current.MainWindow;
                }

                var result = dialog.ShowDialog();

                if (result == true && viewModel.DialogResult)
                {
                    await LoadDataAsync();
                    MessageBox.Show("Ödeme başarıyla güncellendi.", "Başarılı", MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Ödeme düzenleme hatası: {ex.Message}", "Hata", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        [RelayCommand]
        private async Task ViewPaymentDetails(Payment? payment)
        {
            if (payment == null) return;
            MessageBox.Show($"{payment.Description} ödemesi detay özelliği yakında eklenecek!", "Bilgi", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        [RelayCommand]
        private async Task DeletePayment(Payment? payment)
        {
            if (payment == null) return;

            var result = MessageBox.Show($"{payment.Description} ödemesini silmek istediğinize emin misiniz?",
                                       "Ödeme Sil",
                                       MessageBoxButton.YesNo,
                                       MessageBoxImage.Warning);

            if (result == MessageBoxResult.Yes)
            {
                try
                {
                    IsBusy = true;
                    await _paymentService.DeleteAsync(payment.Id);
                    await LoadDataAsync();

                    MessageBox.Show("Ödeme başarıyla silindi.", "Başarılı", MessageBoxButton.OK, MessageBoxImage.Information);
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"Ödeme silinirken hata oluştu: {ex.Message}", "Hata", MessageBoxButton.OK, MessageBoxImage.Error);
                }
                finally
                {
                    IsBusy = false;
                }
            }
        }

        [RelayCommand]
        private void ClearFilters()
        {
            SelectedPaymentType = null;
            SelectedStatus = null;
            SelectedDateRange = "All";
            ApplyFilter();
        }

        [RelayCommand]
        private async Task RefreshData()
        {
            await LoadDataAsync();
        }
    }
}
