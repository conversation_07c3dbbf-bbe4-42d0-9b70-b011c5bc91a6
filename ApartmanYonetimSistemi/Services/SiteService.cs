using ApartmanYonetimSistemi.Data;
using ApartmanYonetimSistemi.Models;
using Microsoft.EntityFrameworkCore;

namespace ApartmanYonetimSistemi.Services
{
    public class SiteService : ISiteService
    {
        private readonly ApartmanDbContext _context;

        public SiteService(ApartmanDbContext context)
        {
            _context = context;
        }

        public async Task<IEnumerable<Site>> GetAllAsync()
        {
            return await _context.Sites
                .Include(s => s.Company)
                .Include(s => s.User)
                .Where(s => s.IsActive)
                .OrderBy(s => s.Name)
                .ToListAsync();
        }

        public async Task<Site?> GetByIdAsync(string id)
        {
            return await _context.Sites
                .Include(s => s.Company)
                .Include(s => s.User)
                .Include(s => s.Apartments)
                .FirstOrDefaultAsync(s => s.Id == id && s.IsActive);
        }

        public async Task<IEnumerable<Site>> GetByUserIdAsync(string userId)
        {
            return await _context.Sites
                .Include(s => s.Company)
                .Include(s => s.User)
                .Where(s => s.UserId == userId && s.IsActive)
                .OrderBy(s => s.Name)
                .ToListAsync();
        }

        public async Task<IEnumerable<Site>> GetByCompanyIdAsync(string? companyId)
        {
            if (string.IsNullOrEmpty(companyId))
                return new List<Site>();

            return await _context.Sites
                .Include(s => s.Company)
                .Include(s => s.User)
                .Where(s => s.CompanyId == companyId && s.IsActive)
                .OrderBy(s => s.Name)
                .ToListAsync();
        }

        public async Task<Site> CreateAsync(Site site)
        {
            site.Id = Guid.NewGuid().ToString();
            site.CreatedAt = DateTime.UtcNow;
            site.UpdatedAt = DateTime.UtcNow;
            site.IsActive = true;

            _context.Sites.Add(site);
            await _context.SaveChangesAsync();
            return site;
        }

        public async Task<Site> UpdateAsync(Site site)
        {
            site.UpdatedAt = DateTime.UtcNow;
            _context.Sites.Update(site);
            await _context.SaveChangesAsync();
            return site;
        }

        public async Task DeleteAsync(string id)
        {
            var site = await _context.Sites.FindAsync(id);
            if (site != null)
            {
                site.IsActive = false;
                site.UpdatedAt = DateTime.UtcNow;
                await _context.SaveChangesAsync();
            }
        }
    }
}
