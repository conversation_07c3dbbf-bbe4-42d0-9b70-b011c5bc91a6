using System.Net;
using System.Net.Mail;
using Microsoft.Extensions.Configuration;

namespace ApartmanYonetimSistemi.Services
{
    public class EmailService : IEmailService
    {
        private readonly IConfiguration _configuration;
        private readonly string _smtpHost;
        private readonly int _smtpPort;
        private readonly string _smtpUsername;
        private readonly string _smtpPassword;
        private readonly string _fromEmail;
        private readonly string _fromName;

        public EmailService(IConfiguration configuration)
        {
            _configuration = configuration;
            _smtpHost = _configuration["Email:SmtpHost"] ?? "smtp.gmail.com";
            _smtpPort = int.Parse(_configuration["Email:SmtpPort"] ?? "587");
            _smtpUsername = _configuration["Email:Username"] ?? "";
            _smtpPassword = _configuration["Email:Password"] ?? "";
            _fromEmail = _configuration["Email:FromEmail"] ?? "";
            _fromName = _configuration["Email:FromName"] ?? "Apartman Yönetim <PERSON>";
        }

        public async Task<bool> SendPasswordResetCodeAsync(string email, string resetCode)
        {
            try
            {
                var subject = "Şifre Sıfırlama Kodu - Apartman Yönetim Sistemi";
                var body = $@"
                    <html>
                    <body style='font-family: Arial, sans-serif;'>
                        <div style='max-width: 600px; margin: 0 auto; padding: 20px;'>
                            <h2 style='color: #2196F3;'>Şifre Sıfırlama</h2>
                            <p>Merhaba,</p>
                            <p>Şifrenizi sıfırlamak için aşağıdaki kodu kullanın:</p>
                            <div style='background-color: #f5f5f5; padding: 15px; text-align: center; margin: 20px 0;'>
                                <h3 style='color: #2196F3; font-size: 24px; margin: 0;'>{resetCode}</h3>
                            </div>
                            <p>Bu kod 15 dakika süreyle geçerlidir.</p>
                            <p>Eğer bu işlemi siz yapmadıysanız, bu e-postayı görmezden gelebilirsiniz.</p>
                            <hr style='margin: 30px 0;'>
                            <p style='color: #666; font-size: 12px;'>Apartman Yönetim Sistemi</p>
                        </div>
                    </body>
                    </html>";

                return await SendEmailAsync(email, subject, body);
            }
            catch
            {
                return false;
            }
        }

        public async Task<bool> SendEmailVerificationCodeAsync(string email, string verificationCode)
        {
            try
            {
                var subject = "E-posta Doğrulama Kodu - Apartman Yönetim Sistemi";
                var body = $@"
                    <html>
                    <body style='font-family: Arial, sans-serif;'>
                        <div style='max-width: 600px; margin: 0 auto; padding: 20px;'>
                            <h2 style='color: #2196F3;'>E-posta Doğrulama</h2>
                            <p>Merhaba,</p>
                            <p>Hesabınızı doğrulamak için aşağıdaki kodu kullanın:</p>
                            <div style='background-color: #f5f5f5; padding: 15px; text-align: center; margin: 20px 0;'>
                                <h3 style='color: #2196F3; font-size: 24px; margin: 0;'>{verificationCode}</h3>
                            </div>
                            <p>Bu kod 30 dakika süreyle geçerlidir.</p>
                            <hr style='margin: 30px 0;'>
                            <p style='color: #666; font-size: 12px;'>Apartman Yönetim Sistemi</p>
                        </div>
                    </body>
                    </html>";

                return await SendEmailAsync(email, subject, body);
            }
            catch
            {
                return false;
            }
        }

        public async Task<bool> SendWelcomeEmailAsync(string email, string firstName)
        {
            try
            {
                var subject = "Hoş Geldiniz - Apartman Yönetim Sistemi";
                var body = $@"
                    <html>
                    <body style='font-family: Arial, sans-serif;'>
                        <div style='max-width: 600px; margin: 0 auto; padding: 20px;'>
                            <h2 style='color: #2196F3;'>Hoş Geldiniz!</h2>
                            <p>Merhaba {firstName},</p>
                            <p>Apartman Yönetim Sistemi'ne başarıyla kayıt oldunuz.</p>
                            <p>Artık sistemi kullanmaya başlayabilirsiniz.</p>
                            <hr style='margin: 30px 0;'>
                            <p style='color: #666; font-size: 12px;'>Apartman Yönetim Sistemi</p>
                        </div>
                    </body>
                    </html>";

                return await SendEmailAsync(email, subject, body);
            }
            catch
            {
                return false;
            }
        }

        private async Task<bool> SendEmailAsync(string toEmail, string subject, string body)
        {
            try
            {
                using var client = new SmtpClient(_smtpHost, _smtpPort);
                client.EnableSsl = true;
                client.Credentials = new NetworkCredential(_smtpUsername, _smtpPassword);

                var message = new MailMessage
                {
                    From = new MailAddress(_fromEmail, _fromName),
                    Subject = subject,
                    Body = body,
                    IsBodyHtml = true
                };

                message.To.Add(toEmail);

                await client.SendMailAsync(message);
                return true;
            }
            catch
            {
                return false;
            }
        }
    }
}
