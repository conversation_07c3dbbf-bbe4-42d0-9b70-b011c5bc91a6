namespace ApartmanYonetimSistemi.Models
{
    public enum UserRole
    {
        <PERSON>Ad<PERSON>,
        Ad<PERSON>,
        Manager,
        Tenant,
        Owner,
        User,
        Realtor
    }

    public enum UserStatus
    {
        Active,
        Inactive,
        Pending,
        Suspended
    }

    public enum UnitType
    {
        Apartment,
        Office,
        Shop,
        Storage,
        Parking
    }

    public enum UnitStatus
    {
        Available,
        Rented,
        OwnerOccupied,
        UnderMaintenance,
        ForSale,
        Reserved
    }

    public enum TenantStatus
    {
        Active,
        MovedOut,
        Pending,
        Suspended
    }

    public enum PaymentType
    {
        Rent,
        Maintenance,
        Utilities,
        Deposit,
        LateFee,
        Other
    }

    public enum PaymentStatus
    {
        Pending,
        Paid,
        Overdue,
        Cancelled,
        Refunded
    }

    public enum PaymentMethod
    {
        Cash,
        BankTransfer,
        CreditCard,
        Check,
        Online
    }
}
