using ApartmanYonetimSistemi.Models;

namespace ApartmanYonetimSistemi.Services
{
    public interface IApartmentService
    {
        Task<IEnumerable<Apartment>> GetAllAsync();
        Task<Apartment?> GetByIdAsync(string id);
        Task<IEnumerable<Apartment>> GetByUserIdAsync(string userId);
        Task<IEnumerable<Apartment>> GetByCompanyIdAsync(string? companyId);
        Task<IEnumerable<Apartment>> GetBySiteIdAsync(string siteId);
        Task<Apartment> CreateAsync(Apartment apartment);
        Task<Apartment> UpdateAsync(Apartment apartment);
        Task DeleteAsync(string id);
    }
}
