using System.Collections.ObjectModel;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using ApartmanYonetimSistemi.Models;
using ApartmanYonetimSistemi.Services;
using System.Threading.Tasks;
using System.Windows;
using System.Text.RegularExpressions;

namespace ApartmanYonetimSistemi.ViewModels.Dialogs
{
    public partial class TenantDialogViewModel : BaseViewModel
    {
        private readonly ITenantService _tenantService;
        private readonly IUnitService _unitService;
        private readonly User _currentUser;
        private Tenant? _editingTenant;

        [ObservableProperty]
        private string _firstName = string.Empty;

        [ObservableProperty]
        private string _lastName = string.Empty;

        [ObservableProperty]
        private string _phoneNumber = string.Empty;

        [ObservableProperty]
        private string _email = string.Empty;

        [ObservableProperty]
        private string? _selectedUnitId;

        [ObservableProperty]
        private DateTime? _moveInDate = DateTime.Today;

        [ObservableProperty]
        private TenantStatus _selectedStatus = TenantStatus.Active;

        [ObservableProperty]
        private string _identityNumber = string.Empty;

        [ObservableProperty]
        private string _emergencyContact = string.Empty;

        [ObservableProperty]
        private string _notes = string.Empty;

        [ObservableProperty]
        private ObservableCollection<Unit> _units = new();

        [ObservableProperty]
        private string _saveButtonText = "Kaydet";

        // Validation Errors
        [ObservableProperty]
        private string _firstNameError = string.Empty;

        [ObservableProperty]
        private string _lastNameError = string.Empty;

        [ObservableProperty]
        private string _phoneNumberError = string.Empty;

        [ObservableProperty]
        private string _emailError = string.Empty;

        public bool DialogResult { get; private set; }

        public TenantDialogViewModel(ITenantService tenantService, IUnitService unitService, User currentUser, Tenant? editingTenant = null)
        {
            _tenantService = tenantService;
            _unitService = unitService;
            _currentUser = currentUser;
            _editingTenant = editingTenant;

            Title = editingTenant == null ? "Yeni Kiracı Ekle" : "Kiracı Düzenle";
            SaveButtonText = editingTenant == null ? "Kaydet" : "Güncelle";

            if (editingTenant != null)
            {
                LoadTenantData();
            }

            _ = LoadUnitsAsync();
        }

        private void LoadTenantData()
        {
            if (_editingTenant == null) return;

            FirstName = _editingTenant.FirstName;
            LastName = _editingTenant.LastName;
            PhoneNumber = _editingTenant.PhoneNumber ?? string.Empty;
            Email = _editingTenant.Email ?? string.Empty;
            SelectedUnitId = _editingTenant.CurrentUnitId;
            MoveInDate = _editingTenant.MoveInDate;
            SelectedStatus = _editingTenant.Status;
            IdentityNumber = _editingTenant.IdentityNumber ?? string.Empty;
            EmergencyContact = _editingTenant.EmergencyContact ?? string.Empty;
            Notes = _editingTenant.Notes ?? string.Empty;
        }

        private async Task LoadUnitsAsync()
        {
            try
            {
                IEnumerable<Unit> unitList;

                if (_currentUser.Role == UserRole.SuperAdmin)
                {
                    unitList = await _unitService.GetAllAsync();
                }
                else if (_currentUser.Role == UserRole.Admin)
                {
                    unitList = await _unitService.GetByCompanyIdAsync(_currentUser.CompanyId);
                }
                else
                {
                    unitList = await _unitService.GetByUserIdAsync(_currentUser.Id);
                }

                Units.Clear();
                foreach (var unit in unitList.OrderBy(u => u.UnitNumber))
                {
                    Units.Add(unit);
                }
            }
            catch (Exception ex)
            {
                // Handle error
            }
        }

        [RelayCommand]
        private async Task Save()
        {
            if (!ValidateForm()) return;

            try
            {
                IsBusy = true;

                var tenant = _editingTenant ?? new Tenant { Id = Guid.NewGuid().ToString() };
                
                tenant.FirstName = FirstName.Trim();
                tenant.LastName = LastName.Trim();
                tenant.PhoneNumber = string.IsNullOrWhiteSpace(PhoneNumber) ? null : PhoneNumber.Trim();
                tenant.Email = string.IsNullOrWhiteSpace(Email) ? null : Email.Trim();
                tenant.CurrentUnitId = SelectedUnitId;
                tenant.MoveInDate = MoveInDate;
                tenant.Status = SelectedStatus;
                tenant.IdentityNumber = string.IsNullOrWhiteSpace(IdentityNumber) ? null : IdentityNumber.Trim();
                tenant.EmergencyContact = string.IsNullOrWhiteSpace(EmergencyContact) ? null : EmergencyContact.Trim();
                tenant.Notes = string.IsNullOrWhiteSpace(Notes) ? null : Notes.Trim();
                tenant.UserId = _currentUser.Id;

                if (_editingTenant == null)
                {
                    await _tenantService.CreateAsync(tenant);
                }
                else
                {
                    await _tenantService.UpdateAsync(tenant);
                }

                DialogResult = true;
                
                // Close dialog
                if (Application.Current.MainWindow?.OwnedWindows.Count > 0)
                {
                    var dialog = Application.Current.MainWindow.OwnedWindows[^1];
                    dialog.DialogResult = true;
                    dialog.Close();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Kiracı kaydedilirken hata oluştu: {ex.Message}", "Hata", MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                IsBusy = false;
            }
        }

        [RelayCommand]
        private void Cancel()
        {
            DialogResult = false;
            
            // Close dialog
            if (Application.Current.MainWindow?.OwnedWindows.Count > 0)
            {
                var dialog = Application.Current.MainWindow.OwnedWindows[^1];
                dialog.DialogResult = false;
                dialog.Close();
            }
        }

        private bool ValidateForm()
        {
            bool isValid = true;

            // First Name validation
            if (string.IsNullOrWhiteSpace(FirstName))
            {
                FirstNameError = "Ad gereklidir";
                isValid = false;
            }
            else if (FirstName.Trim().Length < 2)
            {
                FirstNameError = "Ad en az 2 karakter olmalıdır";
                isValid = false;
            }
            else
            {
                FirstNameError = string.Empty;
            }

            // Last Name validation
            if (string.IsNullOrWhiteSpace(LastName))
            {
                LastNameError = "Soyad gereklidir";
                isValid = false;
            }
            else if (LastName.Trim().Length < 2)
            {
                LastNameError = "Soyad en az 2 karakter olmalıdır";
                isValid = false;
            }
            else
            {
                LastNameError = string.Empty;
            }

            // Phone Number validation
            if (string.IsNullOrWhiteSpace(PhoneNumber))
            {
                PhoneNumberError = "Telefon numarası gereklidir";
                isValid = false;
            }
            else if (!Regex.IsMatch(PhoneNumber.Trim(), @"^(\+90|0)?[5][0-9]{9}$"))
            {
                PhoneNumberError = "Geçerli bir telefon numarası girin";
                isValid = false;
            }
            else
            {
                PhoneNumberError = string.Empty;
            }

            // Email validation (optional but if provided, must be valid)
            if (!string.IsNullOrWhiteSpace(Email))
            {
                if (!Regex.IsMatch(Email.Trim(), @"^[^@\s]+@[^@\s]+\.[^@\s]+$"))
                {
                    EmailError = "Geçerli bir email adresi girin";
                    isValid = false;
                }
                else
                {
                    EmailError = string.Empty;
                }
            }
            else
            {
                EmailError = string.Empty;
            }

            return isValid;
        }
    }
}
