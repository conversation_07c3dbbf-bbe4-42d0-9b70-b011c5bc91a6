using ApartmanYonetimSistemi.Data;
using ApartmanYonetimSistemi.Models;
using Microsoft.EntityFrameworkCore;

namespace ApartmanYonetimSistemi.Services
{
    public class PaymentService : IPaymentService
    {
        private readonly ApartmanDbContext _context;

        public PaymentService(ApartmanDbContext context)
        {
            _context = context;
        }

        public async Task<IEnumerable<Payment>> GetAllAsync()
        {
            return await _context.Payments
                .Include(p => p.Unit)
                .ThenInclude(u => u.Apartment)
                .ThenInclude(a => a.Site)
                .Include(p => p.Tenant)
                .Include(p => p.User)
                .Where(p => p.IsActive)
                .OrderByDescending(p => p.DueDate)
                .ToListAsync();
        }

        public async Task<Payment?> GetByIdAsync(string id)
        {
            return await _context.Payments
                .Include(p => p.Unit)
                .ThenInclude(u => u.Apartment)
                .ThenInclude(a => a.Site)
                .Include(p => p.Tenant)
                .Include(p => p.User)
                .FirstOrDefaultAsync(p => p.Id == id && p.IsActive);
        }

        public async Task<IEnumerable<Payment>> GetByUserIdAsync(string userId)
        {
            return await _context.Payments
                .Include(p => p.Unit)
                .ThenInclude(u => u.Apartment)
                .ThenInclude(a => a.Site)
                .Include(p => p.Tenant)
                .Include(p => p.User)
                .Where(p => p.UserId == userId && p.IsActive)
                .OrderByDescending(p => p.DueDate)
                .ToListAsync();
        }

        public async Task<IEnumerable<Payment>> GetByCompanyIdAsync(string? companyId)
        {
            if (string.IsNullOrEmpty(companyId))
                return new List<Payment>();

            return await _context.Payments
                .Include(p => p.Unit)
                .ThenInclude(u => u.Apartment)
                .ThenInclude(a => a.Site)
                .Include(p => p.Tenant)
                .Include(p => p.User)
                .Where(p => p.Unit.Apartment.Site.CompanyId == companyId && p.IsActive)
                .OrderByDescending(p => p.DueDate)
                .ToListAsync();
        }

        public async Task<IEnumerable<Payment>> GetByUnitIdAsync(string unitId)
        {
            return await _context.Payments
                .Include(p => p.Unit)
                .ThenInclude(u => u.Apartment)
                .ThenInclude(a => a.Site)
                .Include(p => p.Tenant)
                .Include(p => p.User)
                .Where(p => p.UnitId == unitId && p.IsActive)
                .OrderByDescending(p => p.DueDate)
                .ToListAsync();
        }

        public async Task<IEnumerable<Payment>> GetByTenantIdAsync(string tenantId)
        {
            return await _context.Payments
                .Include(p => p.Unit)
                .ThenInclude(u => u.Apartment)
                .ThenInclude(a => a.Site)
                .Include(p => p.Tenant)
                .Include(p => p.User)
                .Where(p => p.TenantId == tenantId && p.IsActive)
                .OrderByDescending(p => p.DueDate)
                .ToListAsync();
        }

        public async Task<IEnumerable<Payment>> GetOverduePaymentsAsync()
        {
            var today = DateTime.Today;
            return await _context.Payments
                .Include(p => p.Unit)
                .ThenInclude(u => u.Apartment)
                .ThenInclude(a => a.Site)
                .Include(p => p.Tenant)
                .Include(p => p.User)
                .Where(p => p.IsActive && p.Status == PaymentStatus.Pending && p.DueDate < today)
                .OrderByDescending(p => p.DueDate)
                .ToListAsync();
        }

        public async Task<Payment> CreateAsync(Payment payment)
        {
            payment.Id = Guid.NewGuid().ToString();
            payment.CreatedAt = DateTime.UtcNow;
            payment.UpdatedAt = DateTime.UtcNow;
            payment.IsActive = true;

            _context.Payments.Add(payment);
            await _context.SaveChangesAsync();
            return payment;
        }

        public async Task<Payment> UpdateAsync(Payment payment)
        {
            payment.UpdatedAt = DateTime.UtcNow;
            _context.Payments.Update(payment);
            await _context.SaveChangesAsync();
            return payment;
        }

        public async Task DeleteAsync(string id)
        {
            var payment = await _context.Payments.FindAsync(id);
            if (payment != null)
            {
                payment.IsActive = false;
                payment.UpdatedAt = DateTime.UtcNow;
                await _context.SaveChangesAsync();
            }
        }

        public async Task<decimal> GetTotalRevenueAsync(string? companyId = null)
        {
            var query = _context.Payments.Where(p => p.IsActive && p.Status == PaymentStatus.Paid);

            if (!string.IsNullOrEmpty(companyId))
            {
                query = query.Where(p => p.Unit.Apartment.Site.CompanyId == companyId);
            }

            return await query.SumAsync(p => p.Amount);
        }

        public async Task<decimal> GetPendingAmountAsync(string? companyId = null)
        {
            var query = _context.Payments.Where(p => p.IsActive && p.Status == PaymentStatus.Pending);

            if (!string.IsNullOrEmpty(companyId))
            {
                query = query.Where(p => p.Unit.Apartment.Site.CompanyId == companyId);
            }

            return await query.SumAsync(p => p.Amount);
        }
    }
}
