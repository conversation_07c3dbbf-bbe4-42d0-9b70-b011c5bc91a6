using ApartmanYonetimSistemi.Data;
using ApartmanYonetimSistemi.Models;
using Microsoft.EntityFrameworkCore;

namespace ApartmanYonetimSistemi.Services
{
    public class UnitService : IUnitService
    {
        private readonly ApartmanDbContext _context;

        public UnitService(ApartmanDbContext context)
        {
            _context = context;
        }

        public async Task<IEnumerable<Unit>> GetAllAsync()
        {
            return await _context.Units
                .Include(u => u.Apartment)
                .Include(u => u.Tenant)
                .Include(u => u.User)
                .Where(u => u.IsActive)
                .OrderBy(u => u.UnitNumber)
                .ToListAsync();
        }

        public async Task<Unit?> GetByIdAsync(string id)
        {
            return await _context.Units
                .Include(u => u.Apartment)
                .Include(u => u.Tenant)
                .Include(u => u.User)
                .FirstOrDefaultAsync(u => u.Id == id && u.IsActive);
        }

        public async Task<IEnumerable<Unit>> GetByUserIdAsync(string userId)
        {
            return await _context.Units
                .Include(u => u.Apartment)
                .Include(u => u.Tenant)
                .Include(u => u.User)
                .Where(u => u.UserId == userId && u.IsActive)
                .OrderBy(u => u.UnitNumber)
                .ToListAsync();
        }

        public async Task<IEnumerable<Unit>> GetByCompanyIdAsync(string? companyId)
        {
            if (string.IsNullOrEmpty(companyId))
                return new List<Unit>();

            return await _context.Units
                .Include(u => u.Apartment)
                .ThenInclude(a => a.Site)
                .Include(u => u.Tenant)
                .Include(u => u.User)
                .Where(u => u.Apartment.Site.CompanyId == companyId && u.IsActive)
                .OrderBy(u => u.UnitNumber)
                .ToListAsync();
        }

        public async Task<IEnumerable<Unit>> GetByApartmentIdAsync(string apartmentId)
        {
            return await _context.Units
                .Include(u => u.Apartment)
                .Include(u => u.Tenant)
                .Include(u => u.User)
                .Where(u => u.ApartmentId == apartmentId && u.IsActive)
                .OrderBy(u => u.UnitNumber)
                .ToListAsync();
        }

        public async Task<Unit> CreateAsync(Unit unit)
        {
            unit.Id = Guid.NewGuid().ToString();
            unit.CreatedAt = DateTime.UtcNow;
            unit.UpdatedAt = DateTime.UtcNow;
            unit.IsActive = true;

            _context.Units.Add(unit);
            await _context.SaveChangesAsync();
            return unit;
        }

        public async Task<Unit> UpdateAsync(Unit unit)
        {
            unit.UpdatedAt = DateTime.UtcNow;
            _context.Units.Update(unit);
            await _context.SaveChangesAsync();
            return unit;
        }

        public async Task DeleteAsync(string id)
        {
            var unit = await _context.Units.FindAsync(id);
            if (unit != null)
            {
                unit.IsActive = false;
                unit.UpdatedAt = DateTime.UtcNow;
                await _context.SaveChangesAsync();
            }
        }
    }
}
