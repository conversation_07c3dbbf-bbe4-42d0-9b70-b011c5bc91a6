using Microsoft.EntityFrameworkCore;
using ApartmanYonetimSistemi.Data;
using ApartmanYonetimSistemi.Models;
using System.Security.Cryptography;
using System.Text;

namespace ApartmanYonetimSistemi.Services
{
    public class UserService : IUserService
    {
        private readonly ApartmanDbContext _context;
        private readonly IEmailService _emailService;

        public UserService(ApartmanDbContext context, IEmailService emailService)
        {
            _context = context;
            _emailService = emailService;
        }

        public async Task<User?> AuthenticateAsync(string email, string password)
        {
            var user = await _context.Users
                .FirstOrDefaultAsync(u => u.Email == email && u.IsActive);

            if (user == null || !VerifyPassword(password, user.PasswordHash))
                return null;

            // Son giriş tarihini güncelle
            user.LastLoginAt = DateTime.UtcNow;
            await _context.SaveChangesAsync();

            return user;
        }

        public async Task<bool> CreateUserAsync(User user, string password)
        {
            try
            {
                // Email kontrolü
                var existingUser = await _context.Users
                    .FirstOrDefaultAsync(u => u.Email == user.Email);

                if (existingUser != null)
                    return false;

                user.Id = Guid.NewGuid().ToString();
                user.PasswordHash = HashPassword(password);
                user.CreatedAt = DateTime.UtcNow;
                user.UpdatedAt = DateTime.UtcNow;

                _context.Users.Add(user);
                await _context.SaveChangesAsync();
                return true;
            }
            catch
            {
                return false;
            }
        }

        public async Task<User?> GetUserByEmailAsync(string email)
        {
            return await _context.Users
                .FirstOrDefaultAsync(u => u.Email == email && u.IsActive);
        }

        public async Task<bool> UpdateUserAsync(User user)
        {
            try
            {
                user.UpdatedAt = DateTime.UtcNow;
                _context.Users.Update(user);
                await _context.SaveChangesAsync();
                return true;
            }
            catch
            {
                return false;
            }
        }

        public async Task<bool> ChangePasswordAsync(string userId, string oldPassword, string newPassword)
        {
            try
            {
                var user = await _context.Users.FindAsync(userId);
                if (user == null || !VerifyPassword(oldPassword, user.PasswordHash))
                    return false;

                user.PasswordHash = HashPassword(newPassword);
                user.UpdatedAt = DateTime.UtcNow;
                await _context.SaveChangesAsync();
                return true;
            }
            catch
            {
                return false;
            }
        }

        private string HashPassword(string password)
        {
            using var sha256 = SHA256.Create();
            var hashedBytes = sha256.ComputeHash(Encoding.UTF8.GetBytes(password + "ApartmanSalt"));
            return Convert.ToBase64String(hashedBytes);
        }

        private bool VerifyPassword(string password, string hash)
        {
            return HashPassword(password) == hash;
        }

        public async Task<bool> SendPasswordResetCodeAsync(string email)
        {
            try
            {
                var user = await GetUserByEmailAsync(email);
                if (user == null)
                    return false;

                // Eski reset token'ları pasif yap
                var oldTokens = await _context.PasswordResetTokens
                    .Where(t => t.Email == email && t.IsActive)
                    .ToListAsync();

                foreach (var token in oldTokens)
                {
                    token.IsActive = false;
                    token.UpdatedAt = DateTime.UtcNow;
                }

                // Yeni reset kodu oluştur
                var resetCode = GenerateResetCode();
                var resetToken = new PasswordResetToken
                {
                    Id = Guid.NewGuid().ToString(),
                    Email = email,
                    ResetCode = resetCode,
                    ExpiresAt = DateTime.UtcNow.AddMinutes(15), // 15 dakika geçerli
                    CreatedAt = DateTime.UtcNow,
                    UpdatedAt = DateTime.UtcNow,
                    IsActive = true
                };

                _context.PasswordResetTokens.Add(resetToken);
                await _context.SaveChangesAsync();

                // Email gönder
                return await _emailService.SendPasswordResetCodeAsync(email, resetCode);
            }
            catch
            {
                return false;
            }
        }

        public async Task<bool> VerifyResetCodeAsync(string email, string resetCode)
        {
            try
            {
                var token = await _context.PasswordResetTokens
                    .FirstOrDefaultAsync(t => t.Email == email &&
                                            t.ResetCode == resetCode &&
                                            t.IsActive &&
                                            !t.IsUsed &&
                                            t.ExpiresAt > DateTime.UtcNow);

                return token != null;
            }
            catch
            {
                return false;
            }
        }

        public async Task<bool> ResetPasswordAsync(string email, string resetCode, string newPassword)
        {
            try
            {
                var token = await _context.PasswordResetTokens
                    .FirstOrDefaultAsync(t => t.Email == email &&
                                            t.ResetCode == resetCode &&
                                            t.IsActive &&
                                            !t.IsUsed &&
                                            t.ExpiresAt > DateTime.UtcNow);

                if (token == null)
                    return false;

                var user = await GetUserByEmailAsync(email);
                if (user == null)
                    return false;

                // Şifreyi güncelle
                user.PasswordHash = HashPassword(newPassword);
                user.UpdatedAt = DateTime.UtcNow;

                // Token'ı kullanılmış olarak işaretle
                token.IsUsed = true;
                token.UsedAt = DateTime.UtcNow;
                token.UpdatedAt = DateTime.UtcNow;

                await _context.SaveChangesAsync();
                return true;
            }
            catch
            {
                return false;
            }
        }

        private string GenerateResetCode()
        {
            var random = new Random();
            return random.Next(100000, 999999).ToString(); // 6 haneli kod
        }

        public RegisterResult Register(string email, string password)
        {
            try
            {
                // E-posta kontrolü
                var existingUser = _context.Users.FirstOrDefault(u => u.Email == email);
                if (existingUser != null)
                {
                    return new RegisterResult { Success = false, ErrorMessage = "Bu e-posta zaten kayıtlı." };
                }

                var user = new User
                {
                    Id = Guid.NewGuid().ToString(),
                    Email = email,
                    PasswordHash = HashPassword(password),
                    CreatedAt = DateTime.UtcNow,
                    UpdatedAt = DateTime.UtcNow,
                    IsActive = true
                };
                _context.Users.Add(user);
                _context.SaveChanges();
                return new RegisterResult { Success = true };
            }
            catch (Exception ex)
            {
                return new RegisterResult { Success = false, ErrorMessage = "Kayıt sırasında hata oluştu: " + ex.Message };
            }
        }
    }
}
