using System.Windows;
using System.Windows.Controls;
using ApartmanYonetimSistemi.ViewModels;
using Microsoft.Extensions.DependencyInjection;

namespace ApartmanYonetimSistemi.Views
{
    public partial class RegisterView : Window
    {
        public RegisterView()
        {
            InitializeComponent();
            
            // ViewModel'i DI container'dan al
            var app = (App)Application.Current;
            DataContext = app.Services.GetRequiredService<RegisterViewModel>();
            
            // PasswordBox event'lerini bağla
            PasswordBox.PasswordChanged += (s, e) =>
            {
                if (DataContext is RegisterViewModel vm)
                    vm.Password = PasswordBox.Password;
            };
            
            ConfirmPasswordBox.PasswordChanged += (s, e) =>
            {
                if (DataContext is RegisterViewModel vm)
                    vm.ConfirmPassword = ConfirmPasswordBox.Password;
            };
        }
    }
}
