using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using ApartmanYonetimSistemi.Services;
using ApartmanYonetimSistemi.Models;
using ApartmanYonetimSistemi.Views;
using System.Windows.Controls;
using System.Windows;
using Microsoft.Extensions.DependencyInjection;

namespace ApartmanYonetimSistemi.ViewModels
{
    public partial class MainViewModel : BaseViewModel
    {
        [ObservableProperty]
        private string _currentUserEmail = string.Empty;

        [ObservableProperty]
        private string _currentUserName = string.Empty;

        [ObservableProperty]
        private UserRole _currentUserRole = UserRole.User;

        [ObservableProperty]
        private bool _isAdminOrSuperAdmin = false;

        [ObservableProperty]
        private UserControl? _currentView;

        private User? _currentUser;
        private readonly IServiceProvider _serviceProvider;

        public MainViewModel(IServiceProvider serviceProvider)
        {
            _serviceProvider = serviceProvider;
            Title = "Apartman Yönetim Sistemi";

            // Başlangıçta dashboard'u göster
            ShowDashboard();
        }

        public void SetCurrentUser(User user)
        {
            _currentUser = user;
            CurrentUserEmail = user.Email;
            CurrentUserName = user.FullName;
            CurrentUserRole = user.Role;
            IsAdminOrSuperAdmin = user.Role == UserRole.Admin || user.Role == UserRole.SuperAdmin;
        }

        [RelayCommand]
        private void ShowDashboard()
        {
            var dashboardView = new DashboardView();
            var dashboardViewModel = _serviceProvider.GetRequiredService<DashboardViewModel>();

            if (_currentUser != null)
            {
                dashboardViewModel.SetCurrentUser(_currentUser);
            }

            dashboardView.DataContext = dashboardViewModel;
            CurrentView = dashboardView;
        }

        [RelayCommand]
        private void ShowSites()
        {
            var sitesView = new SitesView();
            var sitesViewModel = _serviceProvider.GetRequiredService<SitesViewModel>();

            if (_currentUser != null)
            {
                sitesViewModel.SetCurrentUser(_currentUser);
            }

            sitesView.DataContext = sitesViewModel;
            CurrentView = sitesView;
        }

        [RelayCommand]
        private void ShowApartments()
        {
            var apartmentsView = new ApartmentsView();
            var apartmentsViewModel = _serviceProvider.GetRequiredService<ApartmentsViewModel>();

            if (_currentUser != null)
            {
                apartmentsViewModel.SetCurrentUser(_currentUser);
            }

            apartmentsView.DataContext = apartmentsViewModel;
            CurrentView = apartmentsView;
        }

        [RelayCommand]
        private void ShowUnits()
        {
            var unitsView = new UnitsView();
            var unitsViewModel = _serviceProvider.GetRequiredService<UnitsViewModel>();

            if (_currentUser != null)
            {
                unitsViewModel.SetCurrentUser(_currentUser);
            }

            unitsView.DataContext = unitsViewModel;
            CurrentView = unitsView;
        }

        [RelayCommand]
        private void ShowTenants()
        {
            var tenantsView = new TenantsView();
            var tenantsViewModel = _serviceProvider.GetRequiredService<TenantsViewModel>();

            if (_currentUser != null)
            {
                tenantsViewModel.SetCurrentUser(_currentUser);
            }

            tenantsView.DataContext = tenantsViewModel;
            CurrentView = tenantsView;
        }

        [RelayCommand]
        private void ShowPayments()
        {
            var paymentsView = new PaymentsView();
            var paymentsViewModel = _serviceProvider.GetRequiredService<PaymentsViewModel>();

            if (_currentUser != null)
            {
                paymentsViewModel.SetCurrentUser(_currentUser);
            }

            paymentsView.DataContext = paymentsViewModel;
            CurrentView = paymentsView;
        }

        [RelayCommand]
        private void ShowUserManagement()
        {
            if (IsAdminOrSuperAdmin)
            {
                var usersView = new UsersView();
                var usersViewModel = _serviceProvider.GetRequiredService<UsersViewModel>();

                if (_currentUser != null)
                {
                    usersViewModel.SetCurrentUser(_currentUser);
                }

                usersView.DataContext = usersViewModel;
                CurrentView = usersView;
            }
        }

        [RelayCommand]
        private void Logout()
        {
            // Logout işlemi
            Application.Current.Shutdown();
        }

        private UserControl CreateSimpleView(string title, string content)
        {
            var userControl = new UserControl();
            var stackPanel = new StackPanel
            {
                Margin = new Thickness(20)
            };

            var titleBlock = new TextBlock
            {
                Text = title,
                FontSize = 24,
                FontWeight = FontWeights.Bold,
                Margin = new Thickness(0, 0, 0, 20)
            };

            var contentBlock = new TextBlock
            {
                Text = content,
                FontSize = 16
            };

            stackPanel.Children.Add(titleBlock);
            stackPanel.Children.Add(contentBlock);
            userControl.Content = stackPanel;

            return userControl;
        }
    }
}
