using Microsoft.EntityFrameworkCore;
using ApartmanYonetimSistemi.Models;

namespace ApartmanYonetimSistemi.Data
{
    public class ApartmanDbContext : DbContext
    {
        public ApartmanDbContext(DbContextOptions<ApartmanDbContext> options) : base(options)
        {
        }

        // DbSet'ler - Veritabanı tabloları
        public DbSet<User> Users { get; set; }
        public DbSet<Company> Companies { get; set; }
        public DbSet<Site> Sites { get; set; }
        public DbSet<Apartment> Apartments { get; set; }
        public DbSet<Unit> Units { get; set; }
        public DbSet<Tenant> Tenants { get; set; }
        public DbSet<PasswordResetToken> PasswordResetTokens { get; set; }
        public DbSet<Payment> Payments { get; set; }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            base.OnModelCreating(modelBuilder);

            // User-Company relationship
            modelBuilder.Entity<User>()
                .HasOne(u => u.Company)
                .WithMany()
                .HasForeignKey(u => u.CompanyId)
                .OnDelete(DeleteBehavior.SetNull);

            // User tablosu yapılandırması
            modelBuilder.Entity<User>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Email).IsRequired().HasMaxLength(255);
                entity.HasIndex(e => e.Email).IsUnique();
                entity.Property(e => e.Role).HasConversion<int>();
                entity.Property(e => e.Status).HasConversion<string>();
            });

            // Company tablosu yapılandırması
            modelBuilder.Entity<Company>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Name).IsRequired().HasMaxLength(200);
            });

            // Site tablosu yapılandırması
            modelBuilder.Entity<Site>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Name).IsRequired().HasMaxLength(200);
                entity.HasOne(e => e.Company)
                      .WithMany(c => c.Sites)
                      .HasForeignKey(e => e.CompanyId)
                      .OnDelete(DeleteBehavior.SetNull);
                entity.HasOne(e => e.User)
                      .WithMany()
                      .HasForeignKey(e => e.UserId)
                      .OnDelete(DeleteBehavior.Restrict);
            });

            // Apartment tablosu yapılandırması
            modelBuilder.Entity<Apartment>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Name).IsRequired().HasMaxLength(200);
                entity.HasOne(e => e.Site)
                      .WithMany(s => s.Apartments)
                      .HasForeignKey(e => e.SiteId)
                      .OnDelete(DeleteBehavior.Cascade);
                entity.HasOne(e => e.User)
                      .WithMany()
                      .HasForeignKey(e => e.UserId)
                      .OnDelete(DeleteBehavior.Restrict);
            });

            // Unit tablosu yapılandırması
            modelBuilder.Entity<Unit>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.UnitNumber).IsRequired().HasMaxLength(50);
                entity.Property(e => e.Type).HasConversion<string>();
                entity.Property(e => e.Status).HasConversion<string>();
                entity.HasOne(e => e.Apartment)
                      .WithMany(a => a.Units)
                      .HasForeignKey(e => e.ApartmentId)
                      .OnDelete(DeleteBehavior.Cascade);
                entity.HasOne(e => e.Tenant)
                      .WithOne(t => t.CurrentUnit)
                      .HasForeignKey<Unit>(e => e.TenantId)
                      .OnDelete(DeleteBehavior.SetNull);
                entity.HasOne(e => e.User)
                      .WithMany()
                      .HasForeignKey(e => e.UserId)
                      .OnDelete(DeleteBehavior.Restrict);
            });

            // Tenant tablosu yapılandırması
            modelBuilder.Entity<Tenant>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.FirstName).IsRequired().HasMaxLength(100);
                entity.Property(e => e.LastName).IsRequired().HasMaxLength(100);
                entity.Property(e => e.Status).HasConversion<string>();
                entity.HasOne(e => e.User)
                      .WithMany()
                      .HasForeignKey(e => e.UserId)
                      .OnDelete(DeleteBehavior.Restrict);
            });

            // Payment tablosu yapılandırması
            modelBuilder.Entity<Payment>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Description).IsRequired().HasMaxLength(200);
                entity.Property(e => e.Type).HasConversion<string>();
                entity.Property(e => e.Status).HasConversion<string>();
                entity.Property(e => e.Method).HasConversion<string>();
                entity.HasOne(e => e.Unit)
                      .WithMany(u => u.Payments)
                      .HasForeignKey(e => e.UnitId)
                      .OnDelete(DeleteBehavior.Cascade);
                entity.HasOne(e => e.Tenant)
                      .WithMany(t => t.Payments)
                      .HasForeignKey(e => e.TenantId)
                      .OnDelete(DeleteBehavior.SetNull);
                entity.HasOne(e => e.User)
                      .WithMany()
                      .HasForeignKey(e => e.UserId)
                      .OnDelete(DeleteBehavior.Restrict);
            });

            // Seed data (varsayılan veriler)
            SeedData(modelBuilder);
        }

        private void SeedData(ModelBuilder modelBuilder)
        {
            // Varsayılan SuperAdmin kullanıcısı
            modelBuilder.Entity<User>().HasData(
                new User
                {
                    Id = "superadmin-001",
                    Email = "<EMAIL>",
                    FirstName = "Super",
                    LastName = "Admin",
                    Role = UserRole.SuperAdmin,
                    Status = UserStatus.Active,
                    IsEmailVerified = true,
                    PasswordHash = HashPassword("admin123"),
                    CreatedAt = new DateTime(2025, 1, 1, 0, 0, 0, DateTimeKind.Utc),
                    UpdatedAt = new DateTime(2025, 1, 1, 0, 0, 0, DateTimeKind.Utc),
                    IsActive = true
                },
                // Test Admin kullanıcısı
                new User
                {
                    Id = "admin-001",
                    Email = "<EMAIL>",
                    FirstName = "Test",
                    LastName = "Admin",
                    Role = UserRole.Admin,
                    Status = UserStatus.Active,
                    IsEmailVerified = true,
                    PasswordHash = HashPassword("admin123"),
                    CompanyId = "company-001",
                    CompanyName = "Test Emlak A.Ş.",
                    CreatedAt = new DateTime(2025, 1, 1, 0, 0, 0, DateTimeKind.Utc),
                    UpdatedAt = new DateTime(2025, 1, 1, 0, 0, 0, DateTimeKind.Utc),
                    IsActive = true
                },
                // Test Emlakçı kullanıcısı
                new User
                {
                    Id = "realtor-001",
                    Email = "<EMAIL>",
                    FirstName = "Test",
                    LastName = "Emlakçı",
                    Role = UserRole.Realtor,
                    Status = UserStatus.Active,
                    IsEmailVerified = true,
                    PasswordHash = HashPassword("123456"),
                    CompanyId = "company-001",
                    CompanyName = "Test Emlak A.Ş.",
                    CreatedAt = new DateTime(2025, 1, 1, 0, 0, 0, DateTimeKind.Utc),
                    UpdatedAt = new DateTime(2025, 1, 1, 0, 0, 0, DateTimeKind.Utc),
                    IsActive = true
                },
                // Test Normal kullanıcı
                new User
                {
                    Id = "user-001",
                    Email = "<EMAIL>",
                    FirstName = "Test",
                    LastName = "Kullanıcı",
                    Role = UserRole.User,
                    Status = UserStatus.Active,
                    IsEmailVerified = true,
                    PasswordHash = HashPassword("123456"),
                    CreatedAt = new DateTime(2025, 1, 1, 0, 0, 0, DateTimeKind.Utc),
                    UpdatedAt = new DateTime(2025, 1, 1, 0, 0, 0, DateTimeKind.Utc),
                    IsActive = true
                }
            );

            // Test Emlak Şirketi
            modelBuilder.Entity<Company>().HasData(
                new Company
                {
                    Id = "company-001",
                    Name = "Test Emlak A.Ş.",
                    Description = "Test amaçlı emlak şirketi",
                    Email = "<EMAIL>",
                    PhoneNumber = "0212 123 45 67",
                    Address = "Test Mahallesi, Test Sokak No:1, İstanbul",
                    TaxNumber = "1234567890",
                    CreatedAt = new DateTime(2025, 1, 1, 0, 0, 0, DateTimeKind.Utc),
                    UpdatedAt = new DateTime(2025, 1, 1, 0, 0, 0, DateTimeKind.Utc),
                    IsActive = true
                }
            );
        }

        private string HashPassword(string password)
        {
            using var sha256 = System.Security.Cryptography.SHA256.Create();
            var hashedBytes = sha256.ComputeHash(System.Text.Encoding.UTF8.GetBytes(password + "ApartmanSalt"));
            return Convert.ToBase64String(hashedBytes);
        }
    }
}
