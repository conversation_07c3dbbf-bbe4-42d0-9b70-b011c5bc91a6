using Microsoft.EntityFrameworkCore;
using ApartmanYonetimSistemi.Data;
using ApartmanYonetimSistemi.Models;

namespace ApartmanYonetimSistemi.Services
{
    public class ApartmanService : IApartmanService
    {
        private readonly ApartmanDbContext _context;

        public ApartmanService(ApartmanDbContext context)
        {
            _context = context;
        }

        #region Site Operations

        public async Task<List<Site>> GetSitesAsync()
        {
            return await _context.Sites
                .Include(s => s.Apartments)
                .Where(s => s.IsActive)
                .ToListAsync();
        }

        public async Task<Site?> GetSiteByIdAsync(string id)
        {
            return await _context.Sites
                .Include(s => s.Apartments)
                .ThenInclude(a => a.Units)
                .FirstOrDefaultAsync(s => s.Id == id && s.IsActive);
        }

        public async Task<bool> CreateSiteAsync(Site site)
        {
            try
            {
                site.Id = Guid.NewGuid().ToString();
                site.CreatedAt = DateTime.UtcNow;
                site.UpdatedAt = DateTime.UtcNow;

                _context.Sites.Add(site);
                await _context.SaveChangesAsync();
                return true;
            }
            catch
            {
                return false;
            }
        }

        public async Task<bool> UpdateSiteAsync(Site site)
        {
            try
            {
                site.UpdatedAt = DateTime.UtcNow;
                _context.Sites.Update(site);
                await _context.SaveChangesAsync();
                return true;
            }
            catch
            {
                return false;
            }
        }

        public async Task<bool> DeleteSiteAsync(string id)
        {
            try
            {
                var site = await _context.Sites.FindAsync(id);
                if (site != null)
                {
                    site.IsActive = false;
                    site.UpdatedAt = DateTime.UtcNow;
                    await _context.SaveChangesAsync();
                    return true;
                }
                return false;
            }
            catch
            {
                return false;
            }
        }

        #endregion

        #region Apartment Operations

        public async Task<List<Apartment>> GetApartmentsBySiteIdAsync(string siteId)
        {
            return await _context.Apartments
                .Include(a => a.Units)
                .Where(a => a.SiteId == siteId && a.IsActive)
                .ToListAsync();
        }

        public async Task<Apartment?> GetApartmentByIdAsync(string id)
        {
            return await _context.Apartments
                .Include(a => a.Site)
                .Include(a => a.Units)
                .FirstOrDefaultAsync(a => a.Id == id && a.IsActive);
        }

        public async Task<bool> CreateApartmentAsync(Apartment apartment)
        {
            try
            {
                apartment.Id = Guid.NewGuid().ToString();
                apartment.CreatedAt = DateTime.UtcNow;
                apartment.UpdatedAt = DateTime.UtcNow;

                _context.Apartments.Add(apartment);
                await _context.SaveChangesAsync();
                return true;
            }
            catch
            {
                return false;
            }
        }

        public async Task<bool> UpdateApartmentAsync(Apartment apartment)
        {
            try
            {
                apartment.UpdatedAt = DateTime.UtcNow;
                _context.Apartments.Update(apartment);
                await _context.SaveChangesAsync();
                return true;
            }
            catch
            {
                return false;
            }
        }

        public async Task<bool> DeleteApartmentAsync(string id)
        {
            try
            {
                var apartment = await _context.Apartments.FindAsync(id);
                if (apartment != null)
                {
                    apartment.IsActive = false;
                    apartment.UpdatedAt = DateTime.UtcNow;
                    await _context.SaveChangesAsync();
                    return true;
                }
                return false;
            }
            catch
            {
                return false;
            }
        }

        #endregion

        #region Unit Operations

        public async Task<List<Unit>> GetUnitsByApartmentIdAsync(string apartmentId)
        {
            return await _context.Units
                .Include(u => u.Tenant)
                .Where(u => u.ApartmentId == apartmentId && u.IsActive)
                .ToListAsync();
        }

        public async Task<Unit?> GetUnitByIdAsync(string id)
        {
            return await _context.Units
                .Include(u => u.Apartment)
                .Include(u => u.Tenant)
                .FirstOrDefaultAsync(u => u.Id == id && u.IsActive);
        }

        public async Task<bool> CreateUnitAsync(Unit unit)
        {
            try
            {
                unit.Id = Guid.NewGuid().ToString();
                unit.CreatedAt = DateTime.UtcNow;
                unit.UpdatedAt = DateTime.UtcNow;

                _context.Units.Add(unit);
                await _context.SaveChangesAsync();
                return true;
            }
            catch
            {
                return false;
            }
        }

        public async Task<bool> UpdateUnitAsync(Unit unit)
        {
            try
            {
                unit.UpdatedAt = DateTime.UtcNow;
                _context.Units.Update(unit);
                await _context.SaveChangesAsync();
                return true;
            }
            catch
            {
                return false;
            }
        }

        public async Task<bool> DeleteUnitAsync(string id)
        {
            try
            {
                var unit = await _context.Units.FindAsync(id);
                if (unit != null)
                {
                    unit.IsActive = false;
                    unit.UpdatedAt = DateTime.UtcNow;
                    await _context.SaveChangesAsync();
                    return true;
                }
                return false;
            }
            catch
            {
                return false;
            }
        }

        #endregion

        #region Tenant Operations

        public async Task<List<Tenant>> GetTenantsAsync()
        {
            return await _context.Tenants
                .Include(t => t.CurrentUnit)
                .Where(t => t.IsActive)
                .ToListAsync();
        }

        public async Task<Tenant?> GetTenantByIdAsync(string id)
        {
            return await _context.Tenants
                .Include(t => t.CurrentUnit)
                .FirstOrDefaultAsync(t => t.Id == id && t.IsActive);
        }

        public async Task<bool> CreateTenantAsync(Tenant tenant)
        {
            try
            {
                tenant.Id = Guid.NewGuid().ToString();
                tenant.CreatedAt = DateTime.UtcNow;
                tenant.UpdatedAt = DateTime.UtcNow;

                _context.Tenants.Add(tenant);
                await _context.SaveChangesAsync();
                return true;
            }
            catch
            {
                return false;
            }
        }

        public async Task<bool> UpdateTenantAsync(Tenant tenant)
        {
            try
            {
                tenant.UpdatedAt = DateTime.UtcNow;
                _context.Tenants.Update(tenant);
                await _context.SaveChangesAsync();
                return true;
            }
            catch
            {
                return false;
            }
        }

        public async Task<bool> DeleteTenantAsync(string id)
        {
            try
            {
                var tenant = await _context.Tenants.FindAsync(id);
                if (tenant != null)
                {
                    tenant.IsActive = false;
                    tenant.UpdatedAt = DateTime.UtcNow;
                    await _context.SaveChangesAsync();
                    return true;
                }
                return false;
            }
            catch
            {
                return false;
            }
        }

        #endregion

        #region Payment Operations

        public async Task<List<Payment>> GetPaymentsByUnitIdAsync(string unitId)
        {
            return await _context.Payments
                .Include(p => p.Unit)
                .Include(p => p.Tenant)
                .Where(p => p.UnitId == unitId && p.IsActive)
                .ToListAsync();
        }

        public async Task<Payment?> GetPaymentByIdAsync(string id)
        {
            return await _context.Payments
                .Include(p => p.Unit)
                .Include(p => p.Tenant)
                .FirstOrDefaultAsync(p => p.Id == id && p.IsActive);
        }

        public async Task<bool> CreatePaymentAsync(Payment payment)
        {
            try
            {
                payment.Id = Guid.NewGuid().ToString();
                payment.CreatedAt = DateTime.UtcNow;
                payment.UpdatedAt = DateTime.UtcNow;

                _context.Payments.Add(payment);
                await _context.SaveChangesAsync();
                return true;
            }
            catch
            {
                return false;
            }
        }

        public async Task<bool> UpdatePaymentAsync(Payment payment)
        {
            try
            {
                payment.UpdatedAt = DateTime.UtcNow;
                _context.Payments.Update(payment);
                await _context.SaveChangesAsync();
                return true;
            }
            catch
            {
                return false;
            }
        }

        public async Task<bool> DeletePaymentAsync(string id)
        {
            try
            {
                var payment = await _context.Payments.FindAsync(id);
                if (payment != null)
                {
                    payment.IsActive = false;
                    payment.UpdatedAt = DateTime.UtcNow;
                    await _context.SaveChangesAsync();
                    return true;
                }
                return false;
            }
            catch
            {
                return false;
            }
        }

        #endregion
    }
}
