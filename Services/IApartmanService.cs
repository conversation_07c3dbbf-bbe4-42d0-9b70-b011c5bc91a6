using ApartmanYonetimSistemi.Models;

namespace ApartmanYonetimSistemi.Services
{
    public interface IApartmanService
    {
        // Site operations
        Task<List<Site>> GetSitesAsync();
        Task<Site?> GetSiteByIdAsync(string id);
        Task<bool> CreateSiteAsync(Site site);
        Task<bool> UpdateSiteAsync(Site site);
        Task<bool> DeleteSiteAsync(string id);

        // Apartment operations
        Task<List<Apartment>> GetApartmentsBySiteIdAsync(string siteId);
        Task<Apartment?> GetApartmentByIdAsync(string id);
        Task<bool> CreateApartmentAsync(Apartment apartment);
        Task<bool> UpdateApartmentAsync(Apartment apartment);
        Task<bool> DeleteApartmentAsync(string id);

        // Unit operations
        Task<List<Unit>> GetUnitsByApartmentIdAsync(string apartmentId);
        Task<Unit?> GetUnitByIdAsync(string id);
        Task<bool> CreateUnitAsync(Unit unit);
        Task<bool> UpdateUnitAsync(Unit unit);
        Task<bool> DeleteUnitAsync(string id);

        // Tenant operations
        Task<List<Tenant>> GetTenantsAsync();
        Task<Tenant?> GetTenantByIdAsync(string id);
        Task<bool> CreateTenantAsync(Tenant tenant);
        Task<bool> UpdateTenantAsync(Tenant tenant);
        Task<bool> DeleteTenantAsync(string id);

        // Payment operations
        Task<List<Payment>> GetPaymentsByUnitIdAsync(string unitId);
        Task<Payment?> GetPaymentByIdAsync(string id);
        Task<bool> CreatePaymentAsync(Payment payment);
        Task<bool> UpdatePaymentAsync(Payment payment);
        Task<bool> DeletePaymentAsync(string id);
    }
}
