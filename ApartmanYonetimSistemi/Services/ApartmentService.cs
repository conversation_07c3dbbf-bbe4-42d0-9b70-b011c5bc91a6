using ApartmanYonetimSistemi.Data;
using ApartmanYonetimSistemi.Models;
using Microsoft.EntityFrameworkCore;

namespace ApartmanYonetimSistemi.Services
{
    public class ApartmentService : IApartmentService
    {
        private readonly ApartmanDbContext _context;

        public ApartmentService(ApartmanDbContext context)
        {
            _context = context;
        }

        public async Task<IEnumerable<Apartment>> GetAllAsync()
        {
            return await _context.Apartments
                .Include(a => a.Site)
                .Include(a => a.User)
                .Include(a => a.Units)
                .Where(a => a.IsActive)
                .OrderBy(a => a.Name)
                .ToListAsync();
        }

        public async Task<Apartment?> GetByIdAsync(string id)
        {
            return await _context.Apartments
                .Include(a => a.Site)
                .Include(a => a.User)
                .Include(a => a.Units)
                .FirstOrDefaultAsync(a => a.Id == id && a.IsActive);
        }

        public async Task<IEnumerable<Apartment>> GetByUserIdAsync(string userId)
        {
            return await _context.Apartments
                .Include(a => a.Site)
                .Include(a => a.User)
                .Include(a => a.Units)
                .Where(a => a.UserId == userId && a.IsActive)
                .OrderBy(a => a.Name)
                .ToListAsync();
        }

        public async Task<IEnumerable<Apartment>> GetByCompanyIdAsync(string? companyId)
        {
            if (string.IsNullOrEmpty(companyId))
                return new List<Apartment>();

            return await _context.Apartments
                .Include(a => a.Site)
                .Include(a => a.User)
                .Include(a => a.Units)
                .Where(a => a.Site.CompanyId == companyId && a.IsActive)
                .OrderBy(a => a.Name)
                .ToListAsync();
        }

        public async Task<IEnumerable<Apartment>> GetBySiteIdAsync(string siteId)
        {
            return await _context.Apartments
                .Include(a => a.Site)
                .Include(a => a.User)
                .Include(a => a.Units)
                .Where(a => a.SiteId == siteId && a.IsActive)
                .OrderBy(a => a.Name)
                .ToListAsync();
        }

        public async Task<Apartment> CreateAsync(Apartment apartment)
        {
            apartment.Id = Guid.NewGuid().ToString();
            apartment.CreatedAt = DateTime.UtcNow;
            apartment.UpdatedAt = DateTime.UtcNow;
            apartment.IsActive = true;

            _context.Apartments.Add(apartment);
            await _context.SaveChangesAsync();
            return apartment;
        }

        public async Task<Apartment> UpdateAsync(Apartment apartment)
        {
            apartment.UpdatedAt = DateTime.UtcNow;
            _context.Apartments.Update(apartment);
            await _context.SaveChangesAsync();
            return apartment;
        }

        public async Task DeleteAsync(string id)
        {
            var apartment = await _context.Apartments.FindAsync(id);
            if (apartment != null)
            {
                apartment.IsActive = false;
                apartment.UpdatedAt = DateTime.UtcNow;
                await _context.SaveChangesAsync();
            }
        }
    }
}
