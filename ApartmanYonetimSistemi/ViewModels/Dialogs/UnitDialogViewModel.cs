using System.Collections.ObjectModel;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using ApartmanYonetimSistemi.Models;
using ApartmanYonetimSistemi.Services;
using System.Threading.Tasks;
using System.Windows;

namespace ApartmanYonetimSistemi.ViewModels.Dialogs
{
    public partial class UnitDialogViewModel : BaseViewModel
    {
        private readonly IUnitService _unitService;
        private readonly IApartmentService _apartmentService;
        private readonly User _currentUser;
        private Unit? _editingUnit;

        [ObservableProperty]
        private string _unitNumber = string.Empty;

        [ObservableProperty]
        private string? _selectedApartmentId;

        [ObservableProperty]
        private int _floor = 1;

        [ObservableProperty]
        private int _roomCount = 1;

        [ObservableProperty]
        private UnitType _selectedType = UnitType.Apartment;

        [ObservableProperty]
        private UnitStatus _selectedStatus = UnitStatus.Available;

        [ObservableProperty]
        private decimal? _area;

        [ObservableProperty]
        private decimal? _rentPrice;

        [ObservableProperty]
        private string _description = string.Empty;

        [ObservableProperty]
        private ObservableCollection<Apartment> _apartments = new();

        [ObservableProperty]
        private string _saveButtonText = "Kaydet";

        // Validation Errors
        [ObservableProperty]
        private string _unitNumberError = string.Empty;

        [ObservableProperty]
        private string _apartmentError = string.Empty;

        public bool DialogResult { get; private set; }

        public UnitDialogViewModel(IUnitService unitService, IApartmentService apartmentService, User currentUser, Unit? editingUnit = null)
        {
            _unitService = unitService;
            _apartmentService = apartmentService;
            _currentUser = currentUser;
            _editingUnit = editingUnit;

            Title = editingUnit == null ? "Yeni Daire Ekle" : "Daire Düzenle";
            SaveButtonText = editingUnit == null ? "Kaydet" : "Güncelle";

            if (editingUnit != null)
            {
                LoadUnitData();
            }

            _ = LoadApartmentsAsync();
        }

        private void LoadUnitData()
        {
            if (_editingUnit == null) return;

            UnitNumber = _editingUnit.UnitNumber;
            SelectedApartmentId = _editingUnit.ApartmentId;
            Floor = _editingUnit.Floor;
            RoomCount = _editingUnit.RoomCount;
            SelectedType = _editingUnit.Type;
            SelectedStatus = _editingUnit.Status;
            Area = _editingUnit.Area;
            RentPrice = _editingUnit.RentPrice;
            Description = _editingUnit.Description ?? string.Empty;
        }

        private async Task LoadApartmentsAsync()
        {
            try
            {
                IEnumerable<Apartment> apartmentList;

                if (_currentUser.Role == UserRole.SuperAdmin)
                {
                    apartmentList = await _apartmentService.GetAllAsync();
                }
                else if (_currentUser.Role == UserRole.Admin)
                {
                    apartmentList = await _apartmentService.GetByCompanyIdAsync(_currentUser.CompanyId);
                }
                else
                {
                    apartmentList = await _apartmentService.GetByUserIdAsync(_currentUser.Id);
                }

                Apartments.Clear();
                foreach (var apartment in apartmentList.OrderBy(a => a.Name))
                {
                    Apartments.Add(apartment);
                }
            }
            catch (Exception ex)
            {
                // Handle error
            }
        }

        [RelayCommand]
        private async Task Save()
        {
            if (!ValidateForm()) return;

            try
            {
                IsBusy = true;

                var unit = _editingUnit ?? new Unit { Id = Guid.NewGuid().ToString() };
                
                unit.UnitNumber = UnitNumber.Trim();
                unit.ApartmentId = SelectedApartmentId!;
                unit.Floor = Floor;
                unit.RoomCount = RoomCount;
                unit.Type = SelectedType;
                unit.Status = SelectedStatus;
                unit.Area = Area ?? 0;
                unit.RentPrice = RentPrice ?? 0;
                unit.Description = string.IsNullOrWhiteSpace(Description) ? null : Description.Trim();
                unit.UserId = _currentUser.Id;

                if (_editingUnit == null)
                {
                    await _unitService.CreateAsync(unit);
                }
                else
                {
                    await _unitService.UpdateAsync(unit);
                }

                DialogResult = true;
                
                // Close dialog
                if (Application.Current.MainWindow?.OwnedWindows.Count > 0)
                {
                    var dialog = Application.Current.MainWindow.OwnedWindows[^1];
                    dialog.DialogResult = true;
                    dialog.Close();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Daire kaydedilirken hata oluştu: {ex.Message}", "Hata", MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                IsBusy = false;
            }
        }

        [RelayCommand]
        private void Cancel()
        {
            DialogResult = false;
            
            // Close dialog
            if (Application.Current.MainWindow?.OwnedWindows.Count > 0)
            {
                var dialog = Application.Current.MainWindow.OwnedWindows[^1];
                dialog.DialogResult = false;
                dialog.Close();
            }
        }

        private bool ValidateForm()
        {
            bool isValid = true;

            // Unit Number validation
            if (string.IsNullOrWhiteSpace(UnitNumber))
            {
                UnitNumberError = "Daire numarası gereklidir";
                isValid = false;
            }
            else if (UnitNumber.Trim().Length < 1)
            {
                UnitNumberError = "Daire numarası en az 1 karakter olmalıdır";
                isValid = false;
            }
            else
            {
                UnitNumberError = string.Empty;
            }

            // Apartment validation
            if (string.IsNullOrWhiteSpace(SelectedApartmentId))
            {
                ApartmentError = "Apartman seçimi gereklidir";
                isValid = false;
            }
            else
            {
                ApartmentError = string.Empty;
            }

            return isValid;
        }
    }
}
