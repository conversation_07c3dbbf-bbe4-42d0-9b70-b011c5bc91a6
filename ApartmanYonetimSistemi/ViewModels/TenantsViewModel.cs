using System.Collections.ObjectModel;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using ApartmanYonetimSistemi.Models;
using ApartmanYonetimSistemi.Services;
using System.Threading.Tasks;
using System.Windows;
using System.Linq;

namespace ApartmanYonetimSistemi.ViewModels
{
    public partial class TenantsViewModel : BaseViewModel
    {
        private readonly ITenantService _tenantService;

        [ObservableProperty]
        private ObservableCollection<Tenant> _tenants = new();

        [ObservableProperty]
        private ObservableCollection<Tenant> _filteredTenants = new();

        [ObservableProperty]
        private string _searchText = string.Empty;

        [ObservableProperty]
        private TenantStatus? _selectedStatus;

        [ObservableProperty]
        private Tenant? _selectedTenant;

        private User? _currentUser;

        partial void OnSearchTextChanged(string value) => ApplyFilter();
        partial void OnSelectedStatusChanged(TenantStatus? value) => ApplyFilter();

        public TenantsViewModel(ITenantService tenantService)
        {
            _tenantService = tenantService;
            Title = "Kiracı Yönetimi";
        }

        public void SetCurrentUser(User user)
        {
            _currentUser = user;
            _ = LoadDataAsync();
        }

        public async Task LoadDataAsync()
        {
            try
            {
                IsBusy = true;
                Tenants.Clear();

                if (_currentUser == null) return;

                IEnumerable<Tenant> tenantList;

                if (_currentUser.Role == UserRole.SuperAdmin)
                {
                    tenantList = await _tenantService.GetAllAsync();
                }
                else if (_currentUser.Role == UserRole.Admin)
                {
                    tenantList = await _tenantService.GetByCompanyIdAsync(_currentUser.CompanyId);
                }
                else
                {
                    tenantList = await _tenantService.GetByUserIdAsync(_currentUser.Id);
                }

                foreach (var tenant in tenantList.OrderBy(t => t.FirstName).ThenBy(t => t.LastName))
                {
                    Tenants.Add(tenant);
                }

                ApplyFilter();
            }
            catch (Exception ex)
            {
                Tenants.Clear();
                FilteredTenants.Clear();
            }
            finally
            {
                IsBusy = false;
            }
        }

        private void ApplyFilter()
        {
            var query = Tenants.AsEnumerable();

            if (!string.IsNullOrWhiteSpace(SearchText))
            {
                var searchLower = SearchText.ToLower();
                query = query.Where(t => 
                    t.FirstName.ToLower().Contains(searchLower) ||
                    t.LastName.ToLower().Contains(searchLower) ||
                    (t.PhoneNumber?.ToLower().Contains(searchLower) ?? false) ||
                    (t.Email?.ToLower().Contains(searchLower) ?? false));
            }

            if (SelectedStatus.HasValue)
                query = query.Where(t => t.Status == SelectedStatus.Value);

            var filtered = new ObservableCollection<Tenant>();
            foreach (var tenant in query.OrderBy(t => t.FirstName).ThenBy(t => t.LastName))
            {
                filtered.Add(tenant);
            }

            FilteredTenants = filtered;
        }

        [RelayCommand]
        private async Task AddTenant()
        {
            if (_currentUser == null) return;

            try
            {
                var tenantService = Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetRequiredService<ITenantService>(((App)App.Current).Services);
                var unitService = Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetRequiredService<IUnitService>(((App)App.Current).Services);

                var viewModel = new ViewModels.Dialogs.TenantDialogViewModel(tenantService, unitService, _currentUser);
                var dialog = new Views.Dialogs.TenantDialog { DataContext = viewModel };

                if (Application.Current.MainWindow != null)
                {
                    dialog.Owner = Application.Current.MainWindow;
                }

                var result = dialog.ShowDialog();

                if (result == true && viewModel.DialogResult)
                {
                    await LoadDataAsync();
                    MessageBox.Show("Kiracı başarıyla eklendi.", "Başarılı", MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Kiracı ekleme hatası: {ex.Message}", "Hata", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        [RelayCommand]
        private async Task EditTenant(Tenant? tenant)
        {
            if (tenant == null || _currentUser == null) return;

            try
            {
                var tenantService = Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetRequiredService<ITenantService>(((App)App.Current).Services);
                var unitService = Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetRequiredService<IUnitService>(((App)App.Current).Services);

                var viewModel = new ViewModels.Dialogs.TenantDialogViewModel(tenantService, unitService, _currentUser, tenant);
                var dialog = new Views.Dialogs.TenantDialog { DataContext = viewModel };

                if (Application.Current.MainWindow != null)
                {
                    dialog.Owner = Application.Current.MainWindow;
                }

                var result = dialog.ShowDialog();

                if (result == true && viewModel.DialogResult)
                {
                    await LoadDataAsync();
                    MessageBox.Show("Kiracı başarıyla güncellendi.", "Başarılı", MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Kiracı düzenleme hatası: {ex.Message}", "Hata", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        [RelayCommand]
        private async Task ViewTenantDetails(Tenant? tenant)
        {
            if (tenant == null) return;
            MessageBox.Show($"{tenant.FullName} kiracısı detay özelliği yakında eklenecek!", "Bilgi", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        [RelayCommand]
        private async Task DeleteTenant(Tenant? tenant)
        {
            if (tenant == null) return;

            var result = MessageBox.Show($"{tenant.FullName} kiracısını silmek istediğinize emin misiniz?",
                                       "Kiracı Sil",
                                       MessageBoxButton.YesNo,
                                       MessageBoxImage.Warning);

            if (result == MessageBoxResult.Yes)
            {
                try
                {
                    IsBusy = true;
                    await _tenantService.DeleteAsync(tenant.Id);
                    await LoadDataAsync();

                    MessageBox.Show("Kiracı başarıyla silindi.", "Başarılı", MessageBoxButton.OK, MessageBoxImage.Information);
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"Kiracı silinirken hata oluştu: {ex.Message}", "Hata", MessageBoxButton.OK, MessageBoxImage.Error);
                }
                finally
                {
                    IsBusy = false;
                }
            }
        }

        [RelayCommand]
        private void ClearFilters()
        {
            SearchText = string.Empty;
            SelectedStatus = null;
            ApplyFilter();
        }

        [RelayCommand]
        private async Task RefreshData()
        {
            await LoadDataAsync();
        }
    }
}
