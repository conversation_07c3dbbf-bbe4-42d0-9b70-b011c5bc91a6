using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace ApartmanYonetimSistemi.Models
{
    public class Site : BaseEntity
    {
        [Required]
        [StringLength(200)]
        public string Name { get; set; } = string.Empty;

        [StringLength(1000)]
        public string Description { get; set; } = string.Empty;

        [StringLength(200)]
        public string Street { get; set; } = string.Empty;

        [StringLength(100)]
        public string District { get; set; } = string.Empty;

        [StringLength(100)]
        public string City { get; set; } = string.Empty;

        [StringLength(10)]
        public string PostalCode { get; set; } = string.Empty;

        [StringLength(100)]
        public string Country { get; set; } = "Türkiye";

        [StringLength(50)]
        public string CompanyId { get; set; } = string.Empty;

        [StringLength(50)]
        public string OwnerId { get; set; } = string.Empty;

        public int TotalApartments { get; set; } = 0;

        public int TotalUnits { get; set; } = 0;

        // Navigation properties
        public virtual ICollection<Apartment> Apartments { get; set; } = new List<Apartment>();

        [NotMapped]
        public string FullAddress => $"{Street}, {District}, {City} {PostalCode}, {Country}".Trim();
    }
}
