using System.Collections.ObjectModel;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using ApartmanYonetimSistemi.Models;
using ApartmanYonetimSistemi.Services;
using System.Threading.Tasks;
using System.Windows;

namespace ApartmanYonetimSistemi.ViewModels.Dialogs
{
    public partial class SiteDialogViewModel : BaseViewModel
    {
        private readonly ISiteService _siteService;
        private readonly ICompanyService _companyService;
        private readonly User _currentUser;
        private Site? _editingSite;

        [ObservableProperty]
        private string _siteName = string.Empty;

        [ObservableProperty]
        private string _address = string.Empty;

        [ObservableProperty]
        private string _city = string.Empty;

        [ObservableProperty]
        private string _district = string.Empty;

        [ObservableProperty]
        private string _postalCode = string.Empty;

        [ObservableProperty]
        private string _description = string.Empty;

        [ObservableProperty]
        private string? _selectedCompanyId;

        [ObservableProperty]
        private ObservableCollection<Company> _companies = new();

        [ObservableProperty]
        private bool _showCompanySelection = false;

        [ObservableProperty]
        private string _saveButtonText = "Kaydet";

        // Validation Errors
        [ObservableProperty]
        private string _siteNameError = string.Empty;

        [ObservableProperty]
        private string _addressError = string.Empty;

        public bool DialogResult { get; private set; }

        public SiteDialogViewModel(ISiteService siteService, ICompanyService companyService, User currentUser, Site? editingSite = null)
        {
            _siteService = siteService;
            _companyService = companyService;
            _currentUser = currentUser;
            _editingSite = editingSite;

            Title = editingSite == null ? "Yeni Site Ekle" : "Site Düzenle";
            SaveButtonText = editingSite == null ? "Kaydet" : "Güncelle";
            ShowCompanySelection = currentUser.Role == UserRole.SuperAdmin;

            if (editingSite != null)
            {
                LoadSiteData();
            }

            _ = LoadCompaniesAsync();
        }

        private void LoadSiteData()
        {
            if (_editingSite == null) return;

            SiteName = _editingSite.Name;
            Address = _editingSite.Address ?? string.Empty;
            City = _editingSite.City ?? string.Empty;
            District = _editingSite.District ?? string.Empty;
            PostalCode = _editingSite.PostalCode ?? string.Empty;
            Description = _editingSite.Description ?? string.Empty;
            SelectedCompanyId = _editingSite.CompanyId;
        }

        private async Task LoadCompaniesAsync()
        {
            if (!ShowCompanySelection) return;

            try
            {
                var companies = await _companyService.GetAllAsync();
                Companies.Clear();
                foreach (var company in companies)
                {
                    Companies.Add(company);
                }
            }
            catch (Exception ex)
            {
                // Handle error
            }
        }

        [RelayCommand]
        private async Task Save()
        {
            if (!ValidateForm()) return;

            try
            {
                IsBusy = true;

                var site = _editingSite ?? new Site { Id = Guid.NewGuid().ToString() };
                
                site.Name = SiteName.Trim();
                site.Address = Address.Trim();
                site.City = City.Trim();
                site.District = District.Trim();
                site.PostalCode = string.IsNullOrWhiteSpace(PostalCode) ? null : PostalCode.Trim();
                site.Description = string.IsNullOrWhiteSpace(Description) ? null : Description.Trim();
                
                // Company assignment
                if (_currentUser.Role == UserRole.SuperAdmin)
                {
                    site.CompanyId = SelectedCompanyId;
                }
                else
                {
                    site.CompanyId = _currentUser.CompanyId;
                }
                
                site.UserId = _currentUser.Id;

                if (_editingSite == null)
                {
                    await _siteService.CreateAsync(site);
                }
                else
                {
                    await _siteService.UpdateAsync(site);
                }

                DialogResult = true;
                
                // Close dialog
                if (Application.Current.MainWindow?.OwnedWindows.Count > 0)
                {
                    var dialog = Application.Current.MainWindow.OwnedWindows[^1];
                    dialog.DialogResult = true;
                    dialog.Close();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Site kaydedilirken hata oluştu: {ex.Message}", "Hata", MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                IsBusy = false;
            }
        }

        [RelayCommand]
        private void Cancel()
        {
            DialogResult = false;
            
            // Close dialog
            if (Application.Current.MainWindow?.OwnedWindows.Count > 0)
            {
                var dialog = Application.Current.MainWindow.OwnedWindows[^1];
                dialog.DialogResult = false;
                dialog.Close();
            }
        }

        private bool ValidateForm()
        {
            bool isValid = true;

            // Site Name validation
            if (string.IsNullOrWhiteSpace(SiteName))
            {
                SiteNameError = "Site adı gereklidir";
                isValid = false;
            }
            else if (SiteName.Trim().Length < 2)
            {
                SiteNameError = "Site adı en az 2 karakter olmalıdır";
                isValid = false;
            }
            else
            {
                SiteNameError = string.Empty;
            }

            // Address validation
            if (string.IsNullOrWhiteSpace(Address))
            {
                AddressError = "Adres gereklidir";
                isValid = false;
            }
            else if (Address.Trim().Length < 5)
            {
                AddressError = "Adres en az 5 karakter olmalıdır";
                isValid = false;
            }
            else
            {
                AddressError = string.Empty;
            }

            return isValid;
        }
    }
}
