using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using ApartmanYonetimSistemi.Services;
using ApartmanYonetimSistemi.Views;
using System.Windows;
using System.Windows.Media;
using Microsoft.Extensions.DependencyInjection;

namespace ApartmanYonetimSistemi.ViewModels
{
    public partial class LoginViewModel : BaseViewModel
    {
        private readonly IUserService _userService;

        [ObservableProperty]
        private string _email = string.Empty;

        [ObservableProperty]
        private string _statusMessage = string.Empty;

        [ObservableProperty]
        private Brush _statusColor = Brushes.Black;

        public LoginViewModel(IUserService userService)
        {
            _userService = userService;
            Title = "Giriş Yap";
        }

        [RelayCommand]
        private async Task Login(object parameter)
        {
            try
            {
                var passwordBox = parameter as System.Windows.Controls.PasswordBox;
                if (passwordBox == null) return;

                string password = passwordBox.Password;

                if (string.IsNullOrWhiteSpace(Email) || string.IsNullOrWhiteSpace(password))
                {
                    StatusMessage = "Lütfen e-posta ve şifre giriniz.";
                    StatusColor = Brushes.Red;
                    return;
                }

                StatusMessage = "Giriş yapılıyor...";
                StatusColor = Brushes.Black;
                IsBusy = true;

                var user = await _userService.AuthenticateAsync(Email, password);

                if (user != null)
                {
                    StatusMessage = "Giriş başarılı!";
                    StatusColor = Brushes.Green;

                    // Başarılı giriş - Ana pencereyi aç
                    var app = (App)Application.Current;
                    var mainWindow = app.Services.GetRequiredService<MainWindow>();

                    // MainViewModel'e kullanıcı bilgisini aktar
                    if (mainWindow.DataContext is MainViewModel mainViewModel)
                    {
                        mainViewModel.SetCurrentUser(user);
                    }

                    mainWindow.Show();

                    // Login penceresini kapat
                    foreach (Window window in Application.Current.Windows)
                    {
                        if (window.GetType().Name == "LoginWindow")
                        {
                            window.Close();
                            break;
                        }
                    }
                }
                else
                {
                    StatusMessage = "E-posta veya şifre hatalı.";
                    StatusColor = Brushes.Red;
                }
            }
            catch (Exception ex)
            {
                StatusMessage = "Hata: " + ex.Message;
                StatusColor = Brushes.Red;
            }
            finally
            {
                IsBusy = false;
            }
        }

        [RelayCommand]
        private void ForgotPassword()
        {
            var forgotPasswordView = new ForgotPasswordView();
            forgotPasswordView.ShowDialog();
        }

        [RelayCommand]
        private void Register()
        {
            try
            {
                var app = (App)Application.Current;
                var registerView = app.Services.GetRequiredService<RegisterView>();
                var registerViewModel = app.Services.GetRequiredService<RegisterViewModel>();
                registerView.DataContext = registerViewModel;

                registerView.Show();

                // Login penceresini kapat
                foreach (Window window in Application.Current.Windows)
                {
                    if (window.GetType().Name == "LoginWindow")
                    {
                        window.Close();
                        break;
                    }
                }
            }
            catch (Exception ex)
            {
                StatusMessage = "Kayıt sayfası açılırken hata oluştu: " + ex.Message;
                StatusColor = Brushes.Red;
            }
        }
    }
}
