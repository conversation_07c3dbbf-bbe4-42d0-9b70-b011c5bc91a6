using System;
using System.Globalization;
using System.Windows.Data;
using ApartmanYonetimSistemi.Models;

namespace ApartmanYonetimSistemi.Converters
{
    public class UnitStatusConverter : IValueConverter
    {
        public static UnitStatusConverter Instance { get; } = new UnitStatusConverter();

        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is UnitStatus unitStatus)
            {
                return unitStatus switch
                {
                    UnitStatus.Available => "Müsait",
                    UnitStatus.Rented => "Kiralık",
                    UnitStatus.OwnerOccupied => "Mülk Sahibi",
                    UnitStatus.UnderMaintenance => "Bakımda",
                    UnitStatus.ForSale => "Satılık",
                    UnitStatus.Reserved => "Rezerve",
                    _ => value.ToString()
                };
            }
            
            return value?.ToString() ?? string.Empty;
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
}
