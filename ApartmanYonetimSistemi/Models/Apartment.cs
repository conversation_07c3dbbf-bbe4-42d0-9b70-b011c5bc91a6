using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace ApartmanYonetimSistemi.Models
{
    public class Apartment : BaseEntity
    {
        [Required]
        [StringLength(200)]
        public string Name { get; set; } = string.Empty;

        [StringLength(500)]
        public string? Description { get; set; }

        public int FloorCount { get; set; } = 0;

        public int UnitCount { get; set; } = 0;

        public int BuildYear { get; set; } = 0;

        [StringLength(100)]
        public string? BuildingType { get; set; }

        // Foreign Keys
        [Required]
        [StringLength(50)]
        public string SiteId { get; set; } = string.Empty;

        [StringLength(50)]
        public string UserId { get; set; } = string.Empty; // Apartmanı oluşturan kullanıcı

        // Navigation Properties
        [ForeignKey("SiteId")]
        public virtual Site Site { get; set; } = null!;

        [ForeignKey("UserId")]
        public virtual User User { get; set; } = null!;

        public virtual ICollection<Unit> Units { get; set; } = new List<Unit>();
    }
}
