using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace ApartmanYonetimSistemi.Models
{
    public class Tenant : BaseEntity
    {
        [Required]
        [StringLength(100)]
        public string FirstName { get; set; } = string.Empty;

        [Required]
        [StringLength(100)]
        public string LastName { get; set; } = string.Empty;

        [StringLength(20)]
        public string? PhoneNumber { get; set; }

        [StringLength(255)]
        [EmailAddress]
        public string? Email { get; set; }

        [StringLength(11)]
        public string? IdentityNumber { get; set; }

        [StringLength(255)]
        public string? EmergencyContact { get; set; }

        public DateTime? BirthDate { get; set; }

        public TenantStatus Status { get; set; } = TenantStatus.Active;

        public DateTime? MoveInDate { get; set; }

        public DateTime? MoveOutDate { get; set; }

        [Column(TypeName = "decimal(18,2)")]
        public decimal? DepositAmount { get; set; }

        [StringLength(500)]
        public string? Notes { get; set; }

        // Foreign Keys
        [StringLength(50)]
        public string? CurrentUnitId { get; set; }

        [StringLength(50)]
        public string UserId { get; set; } = string.Empty; // Kiracıyı oluşturan kullanıcı

        // Navigation Properties
        [ForeignKey("CurrentUnitId")]
        public virtual Unit? CurrentUnit { get; set; }

        [ForeignKey("UserId")]
        public virtual User User { get; set; } = null!;

        public virtual ICollection<Payment> Payments { get; set; } = new List<Payment>();

        [NotMapped]
        public string FullName => $"{FirstName} {LastName}".Trim();
    }
}
