using ApartmanYonetimSistemi.Data;
using ApartmanYonetimSistemi.Models;
using Microsoft.EntityFrameworkCore;

namespace ApartmanYonetimSistemi.Services
{
    public class CompanyService : ICompanyService
    {
        private readonly ApartmanDbContext _context;

        public CompanyService(ApartmanDbContext context)
        {
            _context = context;
        }

        public async Task<IEnumerable<Company>> GetAllAsync()
        {
            return await _context.Companies
                .Include(c => c.Users)
                .Include(c => c.Sites)
                .Where(c => c.IsActive)
                .OrderBy(c => c.Name)
                .ToListAsync();
        }

        public async Task<Company?> GetByIdAsync(string id)
        {
            return await _context.Companies
                .Include(c => c.Users)
                .Include(c => c.Sites)
                .FirstOrDefaultAsync(c => c.Id == id && c.IsActive);
        }

        public async Task<Company> CreateAsync(Company company)
        {
            company.Id = Guid.NewGuid().ToString();
            company.CreatedAt = DateTime.UtcNow;
            company.UpdatedAt = DateTime.UtcNow;
            company.IsActive = true;

            _context.Companies.Add(company);
            await _context.SaveChangesAsync();
            return company;
        }

        public async Task<Company> UpdateAsync(Company company)
        {
            company.UpdatedAt = DateTime.UtcNow;
            _context.Companies.Update(company);
            await _context.SaveChangesAsync();
            return company;
        }

        public async Task DeleteAsync(string id)
        {
            var company = await _context.Companies.FindAsync(id);
            if (company != null)
            {
                company.IsActive = false;
                company.UpdatedAt = DateTime.UtcNow;
                await _context.SaveChangesAsync();
            }
        }
    }
}
