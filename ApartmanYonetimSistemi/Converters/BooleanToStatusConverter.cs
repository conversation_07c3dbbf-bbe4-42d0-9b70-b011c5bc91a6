using System;
using System.Globalization;
using System.Windows.Data;

namespace ApartmanYonetimSistemi.Converters
{
    public class BooleanToStatusConverter : IValueConverter
    {
        public static readonly BooleanToStatusConverter Instance = new();

        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is bool boolValue)
                return boolValue ? "Aktif" : "Pasif";
            return "Bilinmiyor";
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
}
