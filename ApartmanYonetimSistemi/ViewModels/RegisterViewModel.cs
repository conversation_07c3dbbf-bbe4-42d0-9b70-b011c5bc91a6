using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using ApartmanYonetimSistemi.Services;
using ApartmanYonetimSistemi.Models;
using ApartmanYonetimSistemi.Views;
using System.Windows.Media;
using System.Windows;
using Microsoft.Extensions.DependencyInjection;

namespace ApartmanYonetimSistemi.ViewModels
{
    public partial class RegisterViewModel : BaseViewModel
    {
        private readonly IUserService _userService;

        [ObservableProperty]
        private string _firstName = string.Empty;

        [ObservableProperty]
        private string _lastName = string.Empty;

        [ObservableProperty]
        private string _email = string.Empty;

        [ObservableProperty]
        private string _password = string.Empty;

        [ObservableProperty]
        private string _confirmPassword = string.Empty;

        [ObservableProperty]
        private string _statusMessage = string.Empty;

        [ObservableProperty]
        private Brush _statusColor = Brushes.Black;

        public RegisterViewModel(IUserService userService)
        {
            _userService = userService;
            Title = "Kayıt Ol";
        }

        [RelayCommand]
        private async Task Register()
        {
            if (string.IsNullOrWhiteSpace(FirstName) || string.IsNullOrWhiteSpace(LastName) ||
                string.IsNullOrWhiteSpace(Email) || string.IsNullOrWhiteSpace(Password))
            {
                StatusMessage = "Lütfen tüm alanları doldurunuz.";
                StatusColor = Brushes.Red;
                return;
            }

            if (Password != ConfirmPassword)
            {
                StatusMessage = "Şifreler eşleşmiyor.";
                StatusColor = Brushes.Red;
                return;
            }

            if (Password.Length < 6)
            {
                StatusMessage = "Şifre en az 6 karakter olmalıdır.";
                StatusColor = Brushes.Red;
                return;
            }

            if (!IsValidEmail(Email))
            {
                StatusMessage = "Lütfen geçerli bir e-posta adresi giriniz.";
                StatusColor = Brushes.Red;
                return;
            }

            IsBusy = true;
            StatusMessage = "Kayıt işlemi yapılıyor...";
            StatusColor = Brushes.Black;

            try
            {
                // Create user
                var user = new User
                {
                    FirstName = FirstName.Trim(),
                    LastName = LastName.Trim(),
                    Email = Email.Trim().ToLower(),
                    Role = UserRole.Tenant,
                    Status = UserStatus.Active,
                    IsEmailVerified = false
                };

                var result = await _userService.CreateUserAsync(user, Password);
                if (result)
                {
                    StatusMessage = "Kayıt başarılı! Giriş yapabilirsiniz.";
                    StatusColor = Brushes.Green;
                    
                    // 2 saniye bekle ve giriş sayfasına dön
                    await Task.Delay(2000);
                    BackToLogin();
                }
                else
                {
                    StatusMessage = "Kayıt işlemi başarısız. Bu e-posta adresi zaten kullanılıyor olabilir.";
                    StatusColor = Brushes.Red;
                }
            }
            catch (Exception ex)
            {
                StatusMessage = "Hata: " + ex.Message;
                StatusColor = Brushes.Red;
            }
            finally
            {
                IsBusy = false;
            }
        }

        [RelayCommand]
        private void BackToLogin()
        {
            var app = (App)Application.Current;
            var loginWindow = app.Services.GetRequiredService<Views.LoginWindow>();

            loginWindow.Show();

            // Close current window
            foreach (Window window in Application.Current.Windows)
            {
                if (window.GetType().Name == "RegisterView")
                {
                    window.Close();
                    break;
                }
            }
        }

        private bool IsValidEmail(string email)
        {
            try
            {
                var addr = new System.Net.Mail.MailAddress(email);
                return addr.Address == email;
            }
            catch
            {
                return false;
            }
        }
    }
}
