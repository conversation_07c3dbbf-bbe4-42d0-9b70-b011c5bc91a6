using System.Configuration;
using System.Data;
using System.Windows;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using ApartmanYonetimSistemi.Data;
using ApartmanYonetimSistemi.Services;
using ApartmanYonetimSistemi.ViewModels;
using System.IO;

namespace ApartmanYonetimSistemi;

/// <summary>
/// Interaction logic for App.xaml
/// </summary>
public partial class App : Application
{
    private IHost? _host;
    public IServiceProvider Services { get; private set; } = null!;

    protected override void OnStartup(StartupEventArgs e)
    {
        // Configuration
        var builder = new ConfigurationBuilder()
            .SetBasePath(Directory.GetCurrentDirectory())
            .AddJsonFile("appsettings.json", optional: false, reloadOnChange: true);

        var configuration = builder.Build();

        // Host builder
        var hostBuilder = Host.CreateDefaultBuilder()
            .ConfigureServices((context, services) =>
            {
                // Configuration
                services.AddSingleton<IConfiguration>(configuration);

                // DbContext - SQL Server kullanıyoruz
                services.AddDbContext<ApartmanDbContext>(options =>
                    options.UseSqlServer(context.Configuration.GetConnectionString("DefaultConnection")));

                // Services
                services.AddScoped<IUserService, UserService>();
                services.AddScoped<IEmailService, EmailService>();
                services.AddScoped<IApartmanService, ApartmanService>();

                // ViewModels
                services.AddTransient<MainViewModel>();
                services.AddTransient<LoginViewModel>();
                services.AddTransient<ForgotPasswordViewModel>();
                services.AddTransient<RegisterViewModel>();
                services.AddTransient<DashboardViewModel>();
                services.AddTransient<SitesViewModel>();
                services.AddTransient<ApartmentsViewModel>();
                services.AddTransient<UnitsViewModel>();
                services.AddTransient<TenantsViewModel>();
                services.AddTransient<PaymentsViewModel>();
                services.AddTransient<UsersViewModel>();

                // Dialog ViewModels
                services.AddTransient<ViewModels.Dialogs.SiteDialogViewModel>();
                services.AddTransient<ViewModels.Dialogs.ApartmentDialogViewModel>();
                services.AddTransient<ViewModels.Dialogs.UnitDialogViewModel>();
                services.AddTransient<ViewModels.Dialogs.TenantDialogViewModel>();
                services.AddTransient<ViewModels.Dialogs.PaymentDialogViewModel>();

                // Windows and Views
                services.AddTransient<MainWindow>();
                services.AddTransient<Views.LoginWindow>();
                services.AddTransient<Views.RegisterView>();
                services.AddTransient<Views.ForgotPasswordView>();
                services.AddTransient<Views.DashboardView>();
                services.AddTransient<Views.SitesView>();
                services.AddTransient<Views.ApartmentsView>();
                services.AddTransient<Views.UnitsView>();
                services.AddTransient<Views.TenantsView>();
                services.AddTransient<Views.PaymentsView>();
                services.AddTransient<Views.UsersView>();

                // Dialog Views
                services.AddTransient<Views.Dialogs.SiteDialog>();
                services.AddTransient<Views.Dialogs.ApartmentDialog>();
                services.AddTransient<Views.Dialogs.UnitDialog>();
                services.AddTransient<Views.Dialogs.TenantDialog>();
                services.AddTransient<Views.Dialogs.PaymentDialog>();
            });

        _host = hostBuilder.Build();
        Services = _host.Services;

        base.OnStartup(e);

        // Veritabanını oluştur
        try
        {
            using var scope = Services.CreateScope();
            var context = scope.ServiceProvider.GetRequiredService<ApartmanDbContext>();
            context.Database.EnsureCreated();
        }
        catch (Exception ex)
        {
            MessageBox.Show($"Veritabanı oluşturulurken hata: {ex.Message}", "Hata", MessageBoxButton.OK, MessageBoxImage.Error);
        }

        // Ana pencereyi göster
        var mainWindow = Services.GetRequiredService<Views.LoginWindow>();
        mainWindow.Show();
    }

    protected override void OnExit(ExitEventArgs e)
    {
        _host?.Dispose();
        base.OnExit(e);
    }
}
