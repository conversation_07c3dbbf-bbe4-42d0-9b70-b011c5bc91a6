<Window x:Class="ApartmanYonetimSistemi.Views.Dialogs.TenantDialog"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:converters="clr-namespace:ApartmanYonetimSistemi.Converters"
        Title="{Binding Title}" 
        Height="650" Width="500"
        WindowStartupLocation="CenterOwner"
        ResizeMode="NoResize"
        Background="#F5F5F5">

    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <Border Grid.Row="0" Style="{StaticResource CardStyle}" Margin="0,0,0,20">
            <StackPanel>
                <TextBlock Text="{Binding Title}" Style="{StaticResource TitleStyle}"/>
                <TextBlock Text="Kiracı bilgilerini girin" 
                           Style="{StaticResource SubtitleStyle}" Opacity="0.7"/>
            </StackPanel>
        </Border>

        <!-- Form -->
        <Border Grid.Row="1" Style="{StaticResource CardStyle}" Margin="0,0,0,20">
            <ScrollViewer VerticalScrollBarVisibility="Auto">
                <StackPanel>
                    <!-- First Name and Last Name -->
                    <Grid Margin="0,0,0,15">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="10"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>

                        <!-- First Name -->
                        <StackPanel Grid.Column="0">
                            <TextBlock Text="Ad *" FontWeight="SemiBold" Margin="0,0,0,5"/>
                            <TextBox Text="{Binding FirstName, UpdateSourceTrigger=PropertyChanged}"
                                     Height="35" Padding="10,0"/>
                            <TextBlock Text="{Binding FirstNameError}" 
                                       Foreground="Red" FontSize="12" Margin="0,2,0,0"
                                       Visibility="{Binding FirstNameError, Converter={x:Static converters:StringToVisibilityConverter.Instance}}"/>
                        </StackPanel>

                        <!-- Last Name -->
                        <StackPanel Grid.Column="2">
                            <TextBlock Text="Soyad *" FontWeight="SemiBold" Margin="0,0,0,5"/>
                            <TextBox Text="{Binding LastName, UpdateSourceTrigger=PropertyChanged}"
                                     Height="35" Padding="10,0"/>
                            <TextBlock Text="{Binding LastNameError}" 
                                       Foreground="Red" FontSize="12" Margin="0,2,0,0"
                                       Visibility="{Binding LastNameError, Converter={x:Static converters:StringToVisibilityConverter.Instance}}"/>
                        </StackPanel>
                    </Grid>

                    <!-- Phone Number -->
                    <StackPanel Margin="0,0,0,15">
                        <TextBlock Text="Telefon Numarası *" FontWeight="SemiBold" Margin="0,0,0,5"/>
                        <TextBox Text="{Binding PhoneNumber, UpdateSourceTrigger=PropertyChanged}"
                                 Height="35" Padding="10,0"/>
                        <TextBlock Text="{Binding PhoneNumberError}" 
                                   Foreground="Red" FontSize="12" Margin="0,2,0,0"
                                   Visibility="{Binding PhoneNumberError, Converter={x:Static converters:StringToVisibilityConverter.Instance}}"/>
                    </StackPanel>

                    <!-- Email -->
                    <StackPanel Margin="0,0,0,15">
                        <TextBlock Text="Email" FontWeight="SemiBold" Margin="0,0,0,5"/>
                        <TextBox Text="{Binding Email, UpdateSourceTrigger=PropertyChanged}"
                                 Height="35" Padding="10,0"/>
                        <TextBlock Text="{Binding EmailError}" 
                                   Foreground="Red" FontSize="12" Margin="0,2,0,0"
                                   Visibility="{Binding EmailError, Converter={x:Static converters:StringToVisibilityConverter.Instance}}"/>
                    </StackPanel>

                    <!-- Current Unit -->
                    <StackPanel Margin="0,0,0,15">
                        <TextBlock Text="Daire" FontWeight="SemiBold" Margin="0,0,0,5"/>
                        <ComboBox ItemsSource="{Binding Units}"
                                  SelectedValue="{Binding SelectedUnitId}"
                                  SelectedValuePath="Id"
                                  Height="35">
                            <ComboBox.ItemTemplate>
                                <DataTemplate>
                                    <TextBlock Text="{Binding UnitNumber}" />
                                </DataTemplate>
                            </ComboBox.ItemTemplate>
                        </ComboBox>
                    </StackPanel>

                    <!-- Move In Date and Status -->
                    <Grid Margin="0,0,0,15">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="10"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>

                        <!-- Move In Date -->
                        <StackPanel Grid.Column="0">
                            <TextBlock Text="Giriş Tarihi" FontWeight="SemiBold" Margin="0,0,0,5"/>
                            <DatePicker SelectedDate="{Binding MoveInDate}" Height="35"/>
                        </StackPanel>

                        <!-- Status -->
                        <StackPanel Grid.Column="2">
                            <TextBlock Text="Durum *" FontWeight="SemiBold" Margin="0,0,0,5"/>
                            <ComboBox SelectedValue="{Binding SelectedStatus}" Height="35">
                                <ComboBoxItem Content="Aktif" Tag="Active"/>
                                <ComboBoxItem Content="Taşınmış" Tag="MovedOut"/>
                                <ComboBoxItem Content="Beklemede" Tag="Pending"/>
                                <ComboBoxItem Content="Askıda" Tag="Suspended"/>
                            </ComboBox>
                        </StackPanel>
                    </Grid>

                    <!-- Identity Number -->
                    <StackPanel Margin="0,0,0,15">
                        <TextBlock Text="TC Kimlik No" FontWeight="SemiBold" Margin="0,0,0,5"/>
                        <TextBox Text="{Binding IdentityNumber, UpdateSourceTrigger=PropertyChanged}"
                                 Height="35" Padding="10,0" MaxLength="11"/>
                    </StackPanel>

                    <!-- Emergency Contact -->
                    <StackPanel Margin="0,0,0,15">
                        <TextBlock Text="Acil Durum İletişim" FontWeight="SemiBold" Margin="0,0,0,5"/>
                        <TextBox Text="{Binding EmergencyContact, UpdateSourceTrigger=PropertyChanged}"
                                 Height="35" Padding="10,0"/>
                    </StackPanel>

                    <!-- Notes -->
                    <StackPanel Margin="0,0,0,15">
                        <TextBlock Text="Notlar" FontWeight="SemiBold" Margin="0,0,0,5"/>
                        <TextBox Text="{Binding Notes, UpdateSourceTrigger=PropertyChanged}"
                                 Height="80" Padding="10,5"
                                 TextWrapping="Wrap"
                                 AcceptsReturn="True"
                                 VerticalScrollBarVisibility="Auto"/>
                    </StackPanel>
                </StackPanel>
            </ScrollViewer>
        </Border>

        <!-- Buttons -->
        <Border Grid.Row="2" Style="{StaticResource CardStyle}">
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Right">
                <Button Content="İptal" 
                        Style="{StaticResource SecondaryButton}"
                        Command="{Binding CancelCommand}"
                        Margin="0,0,10,0" 
                        Padding="20,10" 
                        MinWidth="80"/>
                <Button Content="{Binding SaveButtonText}" 
                        Style="{StaticResource PrimaryButton}"
                        Command="{Binding SaveCommand}"
                        Padding="20,10" 
                        MinWidth="80"/>
            </StackPanel>
        </Border>

        <!-- Loading Overlay -->
        <Border Grid.RowSpan="3" 
                Background="#80000000" 
                Visibility="{Binding IsBusy, Converter={x:Static converters:BooleanToVisibilityConverter.Instance}}">
            <StackPanel VerticalAlignment="Center" HorizontalAlignment="Center">
                <ProgressBar IsIndeterminate="True" Width="200" Height="4" Margin="0,0,0,10"/>
                <TextBlock Text="Kaydediliyor..." Foreground="White" FontSize="14" HorizontalAlignment="Center"/>
            </StackPanel>
        </Border>
    </Grid>
</Window>
