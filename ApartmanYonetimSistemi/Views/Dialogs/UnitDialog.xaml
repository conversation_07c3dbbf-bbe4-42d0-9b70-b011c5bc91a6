<Window x:Class="ApartmanYonetimSistemi.Views.Dialogs.UnitDialog"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:converters="clr-namespace:ApartmanYonetimSistemi.Converters"
        Title="{Binding Title}" 
        Height="650" Width="500"
        WindowStartupLocation="CenterOwner"
        ResizeMode="NoResize"
        Background="#F5F5F5">

    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <Border Grid.Row="0" Style="{StaticResource CardStyle}" Margin="0,0,0,20">
            <StackPanel>
                <TextBlock Text="{Binding Title}" Style="{StaticResource TitleStyle}"/>
                <TextBlock Text="Daire bilgilerini girin" 
                           Style="{StaticResource SubtitleStyle}" Opacity="0.7"/>
            </StackPanel>
        </Border>

        <!-- Form -->
        <Border Grid.Row="1" Style="{StaticResource CardStyle}" Margin="0,0,0,20">
            <ScrollViewer VerticalScrollBarVisibility="Auto">
                <StackPanel>
                    <!-- Unit Number -->
                    <StackPanel Margin="0,0,0,15">
                        <TextBlock Text="Daire Numarası *" FontWeight="SemiBold" Margin="0,0,0,5"/>
                        <TextBox Text="{Binding UnitNumber, UpdateSourceTrigger=PropertyChanged}"
                                 Height="35" Padding="10,0"/>
                        <TextBlock Text="{Binding UnitNumberError}" 
                                   Foreground="Red" FontSize="12" Margin="0,2,0,0"
                                   Visibility="{Binding UnitNumberError, Converter={x:Static converters:StringToVisibilityConverter.Instance}}"/>
                    </StackPanel>

                    <!-- Apartment Selection -->
                    <StackPanel Margin="0,0,0,15">
                        <TextBlock Text="Apartman *" FontWeight="SemiBold" Margin="0,0,0,5"/>
                        <ComboBox ItemsSource="{Binding Apartments}"
                                  SelectedValue="{Binding SelectedApartmentId}"
                                  SelectedValuePath="Id"
                                  DisplayMemberPath="Name"
                                  Height="35"/>
                        <TextBlock Text="{Binding ApartmentError}" 
                                   Foreground="Red" FontSize="12" Margin="0,2,0,0"
                                   Visibility="{Binding ApartmentError, Converter={x:Static converters:StringToVisibilityConverter.Instance}}"/>
                    </StackPanel>

                    <!-- Floor and Room Count -->
                    <Grid Margin="0,0,0,15">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="10"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>

                        <!-- Floor -->
                        <StackPanel Grid.Column="0">
                            <TextBlock Text="Kat *" FontWeight="SemiBold" Margin="0,0,0,5"/>
                            <TextBox Text="{Binding Floor, UpdateSourceTrigger=PropertyChanged}"
                                     Height="35" Padding="10,0"/>
                        </StackPanel>

                        <!-- Room Count -->
                        <StackPanel Grid.Column="2">
                            <TextBlock Text="Oda Sayısı *" FontWeight="SemiBold" Margin="0,0,0,5"/>
                            <TextBox Text="{Binding RoomCount, UpdateSourceTrigger=PropertyChanged}"
                                     Height="35" Padding="10,0"/>
                        </StackPanel>
                    </Grid>

                    <!-- Type and Status -->
                    <Grid Margin="0,0,0,15">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="10"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>

                        <!-- Type -->
                        <StackPanel Grid.Column="0">
                            <TextBlock Text="Tür *" FontWeight="SemiBold" Margin="0,0,0,5"/>
                            <ComboBox SelectedValue="{Binding SelectedType}" Height="35">
                                <ComboBoxItem Content="Daire" Tag="Apartment"/>
                                <ComboBoxItem Content="Dükkan" Tag="Shop"/>
                                <ComboBoxItem Content="Ofis" Tag="Office"/>
                                <ComboBoxItem Content="Depo" Tag="Storage"/>
                                <ComboBoxItem Content="Otopark" Tag="Parking"/>
                            </ComboBox>
                        </StackPanel>

                        <!-- Status -->
                        <StackPanel Grid.Column="2">
                            <TextBlock Text="Durum *" FontWeight="SemiBold" Margin="0,0,0,5"/>
                            <ComboBox SelectedValue="{Binding SelectedStatus}" Height="35">
                                <ComboBoxItem Content="Müsait" Tag="Available"/>
                                <ComboBoxItem Content="Kiralanmış" Tag="Rented"/>
                                <ComboBoxItem Content="Mal Sahibi" Tag="OwnerOccupied"/>
                                <ComboBoxItem Content="Bakımda" Tag="UnderMaintenance"/>
                                <ComboBoxItem Content="Satılık" Tag="ForSale"/>
                            </ComboBox>
                        </StackPanel>
                    </Grid>

                    <!-- Area and Rent Price -->
                    <Grid Margin="0,0,0,15">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="10"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>

                        <!-- Area -->
                        <StackPanel Grid.Column="0">
                            <TextBlock Text="Alan (m²)" FontWeight="SemiBold" Margin="0,0,0,5"/>
                            <TextBox Text="{Binding Area, UpdateSourceTrigger=PropertyChanged}"
                                     Height="35" Padding="10,0"/>
                        </StackPanel>

                        <!-- Rent Price -->
                        <StackPanel Grid.Column="2">
                            <TextBlock Text="Kira Bedeli (₺)" FontWeight="SemiBold" Margin="0,0,0,5"/>
                            <TextBox Text="{Binding RentPrice, UpdateSourceTrigger=PropertyChanged}"
                                     Height="35" Padding="10,0"/>
                        </StackPanel>
                    </Grid>

                    <!-- Description -->
                    <StackPanel Margin="0,0,0,15">
                        <TextBlock Text="Açıklama" FontWeight="SemiBold" Margin="0,0,0,5"/>
                        <TextBox Text="{Binding Description, UpdateSourceTrigger=PropertyChanged}"
                                 Height="80" Padding="10,5"
                                 TextWrapping="Wrap"
                                 AcceptsReturn="True"
                                 VerticalScrollBarVisibility="Auto"/>
                    </StackPanel>
                </StackPanel>
            </ScrollViewer>
        </Border>

        <!-- Buttons -->
        <Border Grid.Row="2" Style="{StaticResource CardStyle}">
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Right">
                <Button Content="İptal" 
                        Style="{StaticResource SecondaryButton}"
                        Command="{Binding CancelCommand}"
                        Margin="0,0,10,0" 
                        Padding="20,10" 
                        MinWidth="80"/>
                <Button Content="{Binding SaveButtonText}" 
                        Style="{StaticResource PrimaryButton}"
                        Command="{Binding SaveCommand}"
                        Padding="20,10" 
                        MinWidth="80"/>
            </StackPanel>
        </Border>

        <!-- Loading Overlay -->
        <Border Grid.RowSpan="3" 
                Background="#80000000" 
                Visibility="{Binding IsBusy, Converter={x:Static converters:BooleanToVisibilityConverter.Instance}}">
            <StackPanel VerticalAlignment="Center" HorizontalAlignment="Center">
                <ProgressBar IsIndeterminate="True" Width="200" Height="4" Margin="0,0,0,10"/>
                <TextBlock Text="Kaydediliyor..." Foreground="White" FontSize="14" HorizontalAlignment="Center"/>
            </StackPanel>
        </Border>
    </Grid>
</Window>
