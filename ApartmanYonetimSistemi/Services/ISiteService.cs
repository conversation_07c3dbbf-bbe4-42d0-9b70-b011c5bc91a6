using ApartmanYonetimSistemi.Models;

namespace ApartmanYonetimSistemi.Services
{
    public interface ISiteService
    {
        Task<IEnumerable<Site>> GetAllAsync();
        Task<Site?> GetByIdAsync(string id);
        Task<IEnumerable<Site>> GetByUserIdAsync(string userId);
        Task<IEnumerable<Site>> GetByCompanyIdAsync(string? companyId);
        Task<Site> CreateAsync(Site site);
        Task<Site> UpdateAsync(Site site);
        Task DeleteAsync(string id);
    }
}
