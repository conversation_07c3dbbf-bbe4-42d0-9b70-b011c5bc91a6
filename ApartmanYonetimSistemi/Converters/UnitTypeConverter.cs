using System;
using System.Globalization;
using System.Windows.Data;
using ApartmanYonetimSistemi.Models;

namespace ApartmanYonetimSistemi.Converters
{
    public class UnitTypeConverter : IValueConverter
    {
        public static UnitTypeConverter Instance { get; } = new UnitTypeConverter();

        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is UnitType unitType)
            {
                return unitType switch
                {
                    UnitType.Apartment => "Daire",
                    UnitType.Shop => "Dükkan",
                    UnitType.Office => "Ofis",
                    UnitType.Storage => "Depo",
                    UnitType.Parking => "Otopark",
                    _ => value.ToString()
                };
            }
            
            return value?.ToString() ?? string.Empty;
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
}
