using System.Collections.ObjectModel;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using ApartmanYonetimSistemi.Models;
using ApartmanYonetimSistemi.Services;
using System.Threading.Tasks;
using System.Windows;
using System.Linq;

namespace ApartmanYonetimSistemi.ViewModels
{
    public partial class UnitsViewModel : BaseViewModel
    {
        private readonly IUnitService _unitService;
        private readonly IApartmentService _apartmentService;

        [ObservableProperty]
        private ObservableCollection<Unit> _units = new();

        [ObservableProperty]
        private ObservableCollection<Unit> _filteredUnits = new();

        [ObservableProperty]
        private ObservableCollection<Apartment> _apartments = new();

        [ObservableProperty]
        private string _searchText = string.Empty;

        [ObservableProperty]
        private string? _selectedApartmentId;

        [ObservableProperty]
        private UnitStatus? _selectedStatus;

        [ObservableProperty]
        private Unit? _selectedUnit;

        private User? _currentUser;

        partial void OnSearchTextChanged(string value) => ApplyFilter();
        partial void OnSelectedApartmentIdChanged(string? value) => ApplyFilter();
        partial void OnSelectedStatusChanged(UnitStatus? value) => ApplyFilter();

        public UnitsViewModel(IUnitService unitService, IApartmentService apartmentService)
        {
            _unitService = unitService;
            _apartmentService = apartmentService;
            Title = "Daire Yönetimi";
        }

        public void SetCurrentUser(User user)
        {
            _currentUser = user;
            _ = LoadDataAsync();
        }

        public async Task LoadDataAsync()
        {
            try
            {
                IsBusy = true;
                Units.Clear();
                Apartments.Clear();

                if (_currentUser == null) return;

                // Apartmanları yükle
                IEnumerable<Apartment> apartmentList;
                IEnumerable<Unit> unitList;

                if (_currentUser.Role == UserRole.SuperAdmin)
                {
                    apartmentList = await _apartmentService.GetAllAsync();
                    unitList = await _unitService.GetAllAsync();
                }
                else if (_currentUser.Role == UserRole.Admin)
                {
                    apartmentList = await _apartmentService.GetByCompanyIdAsync(_currentUser.CompanyId);
                    unitList = await _unitService.GetByCompanyIdAsync(_currentUser.CompanyId);
                }
                else
                {
                    apartmentList = await _apartmentService.GetByUserIdAsync(_currentUser.Id);
                    unitList = await _unitService.GetByUserIdAsync(_currentUser.Id);
                }

                // Apartman listesini doldur
                foreach (var apartment in apartmentList.OrderBy(a => a.Name))
                {
                    Apartments.Add(apartment);
                }

                // Daire listesini doldur
                foreach (var unit in unitList.OrderBy(u => u.UnitNumber))
                {
                    Units.Add(unit);
                }

                ApplyFilter();
            }
            catch (Exception ex)
            {
                Units.Clear();
                FilteredUnits.Clear();
                Apartments.Clear();
            }
            finally
            {
                IsBusy = false;
            }
        }

        private void ApplyFilter()
        {
            var query = Units.AsEnumerable();

            if (!string.IsNullOrWhiteSpace(SearchText))
                query = query.Where(u => u.UnitNumber.ToLower().Contains(SearchText.ToLower()) ||
                                        (u.Description?.ToLower().Contains(SearchText.ToLower()) ?? false));

            if (!string.IsNullOrWhiteSpace(SelectedApartmentId))
                query = query.Where(u => u.ApartmentId == SelectedApartmentId);

            if (SelectedStatus.HasValue)
                query = query.Where(u => u.Status == SelectedStatus.Value);

            var filtered = new ObservableCollection<Unit>();
            foreach (var unit in query.OrderBy(u => u.UnitNumber))
            {
                filtered.Add(unit);
            }

            FilteredUnits = filtered;
        }

        [RelayCommand]
        private async Task AddUnit()
        {
            if (_currentUser == null) return;

            try
            {
                var unitService = Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetRequiredService<IUnitService>(((App)App.Current).Services);
                var apartmentService = Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetRequiredService<IApartmentService>(((App)App.Current).Services);

                var viewModel = new ViewModels.Dialogs.UnitDialogViewModel(unitService, apartmentService, _currentUser);
                var dialog = new Views.Dialogs.UnitDialog { DataContext = viewModel };

                if (Application.Current.MainWindow != null)
                {
                    dialog.Owner = Application.Current.MainWindow;
                }

                var result = dialog.ShowDialog();

                if (result == true && viewModel.DialogResult)
                {
                    await LoadDataAsync();
                    MessageBox.Show("Daire başarıyla eklendi.", "Başarılı", MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Daire ekleme hatası: {ex.Message}", "Hata", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        [RelayCommand]
        private async Task EditUnit(Unit? unit)
        {
            if (unit == null || _currentUser == null) return;

            try
            {
                var unitService = Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetRequiredService<IUnitService>(((App)App.Current).Services);
                var apartmentService = Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetRequiredService<IApartmentService>(((App)App.Current).Services);

                var viewModel = new ViewModels.Dialogs.UnitDialogViewModel(unitService, apartmentService, _currentUser, unit);
                var dialog = new Views.Dialogs.UnitDialog { DataContext = viewModel };

                if (Application.Current.MainWindow != null)
                {
                    dialog.Owner = Application.Current.MainWindow;
                }

                var result = dialog.ShowDialog();

                if (result == true && viewModel.DialogResult)
                {
                    await LoadDataAsync();
                    MessageBox.Show("Daire başarıyla güncellendi.", "Başarılı", MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Daire düzenleme hatası: {ex.Message}", "Hata", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        [RelayCommand]
        private async Task ViewUnitDetails(Unit? unit)
        {
            if (unit == null) return;
            MessageBox.Show($"{unit.UnitNumber} dairesi detay özelliği yakında eklenecek!", "Bilgi", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        [RelayCommand]
        private async Task DeleteUnit(Unit? unit)
        {
            if (unit == null) return;

            var result = MessageBox.Show($"{unit.UnitNumber} dairesini silmek istediğinize emin misiniz?",
                                       "Daire Sil",
                                       MessageBoxButton.YesNo,
                                       MessageBoxImage.Warning);

            if (result == MessageBoxResult.Yes)
            {
                try
                {
                    IsBusy = true;
                    await _unitService.DeleteAsync(unit.Id);
                    await LoadDataAsync();

                    MessageBox.Show("Daire başarıyla silindi.", "Başarılı", MessageBoxButton.OK, MessageBoxImage.Information);
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"Daire silinirken hata oluştu: {ex.Message}", "Hata", MessageBoxButton.OK, MessageBoxImage.Error);
                }
                finally
                {
                    IsBusy = false;
                }
            }
        }

        [RelayCommand]
        private void ClearFilters()
        {
            SearchText = string.Empty;
            SelectedApartmentId = null;
            SelectedStatus = null;
            ApplyFilter();
        }

        [RelayCommand]
        private async Task RefreshData()
        {
            await LoadDataAsync();
        }
    }
}
