using Microsoft.EntityFrameworkCore;
using ApartmanYonetimSistemi.Models;

namespace ApartmanYonetimSistemi.Data
{
    public class ApartmanDbContext : DbContext
    {
        public ApartmanDbContext(DbContextOptions<ApartmanDbContext> options) : base(options)
        {
        }

        // DbSet'ler - Veritabanı tabloları
        public DbSet<User> Users { get; set; }
        public DbSet<Site> Sites { get; set; }
        public DbSet<Apartment> Apartments { get; set; }
        public DbSet<Unit> Units { get; set; }
        public DbSet<Tenant> Tenants { get; set; }
        public DbSet<Payment> Payments { get; set; }
        public DbSet<PasswordResetToken> PasswordResetTokens { get; set; }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            base.OnModelCreating(modelBuilder);

            // User tablosu yapılandırması
            modelBuilder.Entity<User>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Email).IsRequired().HasMaxLength(255);
                entity.HasIndex(e => e.Email).IsUnique();
                entity.Property(e => e.Role).HasConversion<string>();
                entity.Property(e => e.Status).HasConversion<string>();
            });

            // Site tablosu yapılandırması
            modelBuilder.Entity<Site>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Name).IsRequired().HasMaxLength(200);

                // İlişkiler
                entity.HasMany(s => s.Apartments)
                      .WithOne(a => a.Site)
                      .HasForeignKey(a => a.SiteId)
                      .OnDelete(DeleteBehavior.Cascade);
            });

            // Apartment tablosu yapılandırması
            modelBuilder.Entity<Apartment>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Name).IsRequired().HasMaxLength(200);
                entity.Property(e => e.SiteId).IsRequired().HasMaxLength(50);

                // İlişkiler
                entity.HasMany(a => a.Units)
                      .WithOne(u => u.Apartment)
                      .HasForeignKey(u => u.ApartmentId)
                      .OnDelete(DeleteBehavior.Cascade);
            });

            // Unit tablosu yapılandırması
            modelBuilder.Entity<Unit>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.UnitNumber).IsRequired().HasMaxLength(10);
                entity.Property(e => e.ApartmentId).IsRequired().HasMaxLength(50);
                entity.Property(e => e.Type).HasConversion<string>();
                entity.Property(e => e.Status).HasConversion<string>();

                // Decimal precision
                entity.Property(e => e.RentAmount).HasPrecision(18, 2);
                entity.Property(e => e.MaintenanceFee).HasPrecision(18, 2);
                entity.Property(e => e.Size).HasPrecision(8, 2);

                // İlişkiler
                entity.HasOne(u => u.CurrentTenant)
                      .WithOne(t => t.Unit)
                      .HasForeignKey<Tenant>(t => t.UnitId)
                      .OnDelete(DeleteBehavior.SetNull);

                entity.HasMany(u => u.Payments)
                      .WithOne(p => p.Unit)
                      .HasForeignKey(p => p.UnitId)
                      .OnDelete(DeleteBehavior.Cascade);
            });

            // Tenant tablosu yapılandırması
            modelBuilder.Entity<Tenant>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.FirstName).IsRequired().HasMaxLength(100);
                entity.Property(e => e.LastName).IsRequired().HasMaxLength(100);
                entity.Property(e => e.Status).HasConversion<string>();

                // İlişkiler
                entity.HasMany(t => t.Payments)
                      .WithOne(p => p.Tenant)
                      .HasForeignKey(p => p.TenantId)
                      .OnDelete(DeleteBehavior.SetNull);
            });

            // Payment tablosu yapılandırması
            modelBuilder.Entity<Payment>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.UnitId).IsRequired().HasMaxLength(50);
                entity.Property(e => e.Type).HasConversion<string>();
                entity.Property(e => e.Status).HasConversion<string>();
                entity.Property(e => e.PaymentMethod).HasConversion<string>();

                // Decimal precision
                entity.Property(e => e.Amount).HasPrecision(18, 2);
                entity.Property(e => e.TotalAmount).HasPrecision(18, 2);
                entity.Property(e => e.LateFee).HasPrecision(18, 2);
                entity.Property(e => e.Discount).HasPrecision(18, 2);
            });

            // Seed data (varsayılan veriler)
            SeedData(modelBuilder);
        }

        private void SeedData(ModelBuilder modelBuilder)
        {
            // Varsayılan SuperAdmin kullanıcısı
            modelBuilder.Entity<User>().HasData(
                new User
                {
                    Id = "superadmin-001",
                    Email = "<EMAIL>",
                    FirstName = "Super",
                    LastName = "Admin",
                    Role = UserRole.SuperAdmin,
                    Status = UserStatus.Active,
                    IsEmailVerified = true,
                    PasswordHash = HashPassword("admin123"), // Basit hash - production'da güçlü hash kullanın
                    CreatedAt = DateTime.UtcNow,
                    UpdatedAt = DateTime.UtcNow,
                    IsActive = true
                }
            );
        }

        private string HashPassword(string password)
        {
            // Basit hash - production'da BCrypt veya Argon2 kullanın
            using var sha256 = System.Security.Cryptography.SHA256.Create();
            var hashedBytes = sha256.ComputeHash(System.Text.Encoding.UTF8.GetBytes(password + "ApartmanSalt"));
            return Convert.ToBase64String(hashedBytes);
        }
    }
}
