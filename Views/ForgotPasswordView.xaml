<Window x:Class="ApartmanYonetimSistemi.Views.ForgotPasswordView"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:converters="clr-namespace:ApartmanYonetimSistemi.Converters"
        Title="Şifremi Unuttum" Height="500" Width="600"
        WindowStartupLocation="CenterOwner"
        ResizeMode="NoResize"
        Background="#F5F5F5">

    <Window.Resources>
        <converters:InverseBooleanConverter x:Key="InverseBooleanConverter"/>
        <converters:BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter"/>
        <!-- Modern Button Style -->
        <Style x:Key="ModernButton" TargetType="Button">
            <Setter Property="Background" Value="#2196F3"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Padding" Value="20,12"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}"
                                CornerRadius="5"
                                BorderThickness="{TemplateBinding BorderThickness}"
                                BorderBrush="{TemplateBinding BorderBrush}">
                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="#1976D2"/>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter Property="Background" Value="#0D47A1"/>
                            </Trigger>
                            <Trigger Property="IsEnabled" Value="False">
                                <Setter Property="Background" Value="#CCCCCC"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- Modern TextBox Style -->
        <Style x:Key="ModernTextBox" TargetType="TextBox">
            <Setter Property="Padding" Value="10,8"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="BorderBrush" Value="#CCCCCC"/>
            <Setter Property="Background" Value="White"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="TextBox">
                        <Border Background="{TemplateBinding Background}"
                                BorderBrush="{TemplateBinding BorderBrush}"
                                BorderThickness="{TemplateBinding BorderThickness}"
                                CornerRadius="5">
                            <ScrollViewer x:Name="PART_ContentHost"
                                        Margin="{TemplateBinding Padding}"
                                        VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsFocused" Value="True">
                                <Setter Property="BorderBrush" Value="#2196F3"/>
                                <Setter Property="BorderThickness" Value="2"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- Modern PasswordBox Style -->
        <Style x:Key="ModernPasswordBox" TargetType="PasswordBox">
            <Setter Property="Padding" Value="10,8"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="BorderBrush" Value="#CCCCCC"/>
            <Setter Property="Background" Value="White"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="PasswordBox">
                        <Border Background="{TemplateBinding Background}"
                                BorderBrush="{TemplateBinding BorderBrush}"
                                BorderThickness="{TemplateBinding BorderThickness}"
                                CornerRadius="5">
                            <ScrollViewer x:Name="PART_ContentHost"
                                        Margin="{TemplateBinding Padding}"
                                        VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsFocused" Value="True">
                                <Setter Property="BorderBrush" Value="#2196F3"/>
                                <Setter Property="BorderThickness" Value="2"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
    </Window.Resources>

    <Grid>
        <Border Background="White"
                CornerRadius="15"
                Margin="40"
                Padding="40">
            <Border.Effect>
                <DropShadowEffect Color="Gray" Direction="315" ShadowDepth="5" Opacity="0.3" BlurRadius="10"/>
            </Border.Effect>

            <StackPanel>
                <!-- Başlık -->
                <StackPanel HorizontalAlignment="Center" Margin="0,0,0,30">
                    <Ellipse Width="60" Height="60" Fill="#2196F3" Margin="0,0,0,15">
                        <Ellipse.Effect>
                            <DropShadowEffect Color="Gray" Direction="315" ShadowDepth="3" Opacity="0.3" BlurRadius="8"/>
                        </Ellipse.Effect>
                    </Ellipse>
                    <TextBlock Text="🔑" FontSize="30" HorizontalAlignment="Center" Margin="0,-45,0,0" Foreground="White"/>
                    
                    <TextBlock Text="Şifremi Unuttum"
                               FontSize="24"
                               FontWeight="Bold"
                               Foreground="#1976D2"
                               HorizontalAlignment="Center"
                               Margin="0,0,0,5"/>
                    <TextBlock Text="E-posta adresinize sıfırlama kodu göndereceğiz"
                               FontSize="14"
                               Foreground="#666666"
                               HorizontalAlignment="Center"/>
                </StackPanel>

                <!-- Form -->
                <StackPanel MaxWidth="400">
                    <!-- E-posta Adresi -->
                    <TextBlock Text="E-posta Adresi"
                               FontSize="14"
                               FontWeight="SemiBold"
                               Foreground="#333333"
                               Margin="0,0,0,8"/>
                    <TextBox Text="{Binding Email, UpdateSourceTrigger=PropertyChanged}"
                             Style="{StaticResource ModernTextBox}"
                             IsEnabled="{Binding IsCodeSent, Converter={StaticResource InverseBooleanConverter}}"
                             Margin="0,0,0,15"/>

                    <!-- Kod Gönder Butonu -->
                    <Button Content="Sıfırlama Kodu Gönder"
                            Command="{Binding SendResetCodeCommand}"
                            Style="{StaticResource ModernButton}"
                            IsEnabled="{Binding IsCodeSent, Converter={StaticResource InverseBooleanConverter}}"
                            Visibility="{Binding IsCodeSent, Converter={StaticResource BooleanToVisibilityConverter}, ConverterParameter=Inverse}"
                            Height="45"
                            Margin="0,0,0,20"/>

                    <!-- Sıfırlama Kodu -->
                    <TextBlock Text="Sıfırlama Kodu"
                               FontSize="14"
                               FontWeight="SemiBold"
                               Foreground="#333333"
                               Margin="0,0,0,8"
                               Visibility="{Binding IsCodeSent, Converter={StaticResource BooleanToVisibilityConverter}}"/>
                    <TextBox Text="{Binding ResetCode, UpdateSourceTrigger=PropertyChanged}"
                             Style="{StaticResource ModernTextBox}"
                             MaxLength="6"
                             IsEnabled="{Binding IsCodeVerified, Converter={StaticResource InverseBooleanConverter}}"
                             Visibility="{Binding IsCodeSent, Converter={StaticResource BooleanToVisibilityConverter}}"
                             Margin="0,0,0,15"/>

                    <!-- Kod Doğrula Butonu -->
                    <Button Content="Kodu Doğrula"
                            Command="{Binding VerifyCodeCommand}"
                            Style="{StaticResource ModernButton}"
                            IsEnabled="{Binding IsCodeVerified, Converter={StaticResource InverseBooleanConverter}}"
                            Visibility="{Binding IsCodeSent, Converter={StaticResource BooleanToVisibilityConverter}}"
                            Height="45"
                            Margin="0,0,0,20"/>

                    <!-- Yeni Şifre -->
                    <TextBlock Text="Yeni Şifre"
                               FontSize="14"
                               FontWeight="SemiBold"
                               Foreground="#333333"
                               Margin="0,0,0,8"
                               Visibility="{Binding IsCodeVerified, Converter={StaticResource BooleanToVisibilityConverter}}"/>
                    <PasswordBox x:Name="NewPasswordBox"
                                 Style="{StaticResource ModernPasswordBox}"
                                 Visibility="{Binding IsCodeVerified, Converter={StaticResource BooleanToVisibilityConverter}}"
                                 PasswordChanged="NewPasswordBox_PasswordChanged"
                                 Margin="0,0,0,15"/>

                    <!-- Şifre Tekrar -->
                    <TextBlock Text="Şifre Tekrar"
                               FontSize="14"
                               FontWeight="SemiBold"
                               Foreground="#333333"
                               Margin="0,0,0,8"
                               Visibility="{Binding IsCodeVerified, Converter={StaticResource BooleanToVisibilityConverter}}"/>
                    <PasswordBox x:Name="ConfirmPasswordBox"
                                 Style="{StaticResource ModernPasswordBox}"
                                 Visibility="{Binding IsCodeVerified, Converter={StaticResource BooleanToVisibilityConverter}}"
                                 PasswordChanged="ConfirmPasswordBox_PasswordChanged"
                                 Margin="0,0,0,15"/>

                    <!-- Şifre Sıfırla Butonu -->
                    <Button Content="Şifreyi Sıfırla"
                            Command="{Binding ResetPasswordCommand}"
                            Style="{StaticResource ModernButton}"
                            Visibility="{Binding IsCodeVerified, Converter={StaticResource BooleanToVisibilityConverter}}"
                            Height="45"
                            Margin="0,0,0,20"/>

                    <!-- Geri Dön Butonu -->
                    <Button Content="Giriş Sayfasına Dön"
                            Command="{Binding BackToLoginCommand}"
                            Style="{StaticResource ModernButton}"
                            Background="#666666"
                            Height="40"
                            Margin="0,10,0,0"/>

                    <!-- Durum Mesajı -->
                    <TextBlock Text="{Binding StatusMessage}"
                               Foreground="{Binding StatusColor}"
                               FontSize="12"
                               FontWeight="SemiBold"
                               HorizontalAlignment="Center"
                               TextAlignment="Center"
                               TextWrapping="Wrap"
                               Margin="0,20,0,0"/>
                </StackPanel>
            </StackPanel>
        </Border>
    </Grid>
</Window>
