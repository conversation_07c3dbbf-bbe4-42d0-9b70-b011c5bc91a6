using System.Collections.ObjectModel;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using ApartmanYonetimSistemi.Models;
using ApartmanYonetimSistemi.Services;
using System.Threading.Tasks;
using System.Windows;
using System.Linq;

namespace ApartmanYonetimSistemi.ViewModels
{
    public partial class ApartmentsViewModel : BaseViewModel
    {
        private readonly IApartmentService _apartmentService;
        private readonly ISiteService _siteService;

        [ObservableProperty]
        private ObservableCollection<Apartment> _apartments = new();

        [ObservableProperty]
        private ObservableCollection<Apartment> _filteredApartments = new();

        [ObservableProperty]
        private string _searchText = string.Empty;

        [ObservableProperty]
        private Site? _selectedSite;

        [ObservableProperty]
        private ObservableCollection<Site> _siteList = new();

        private User? _currentUser;

        partial void OnSearchTextChanged(string value) => ApplyFilter();
        partial void OnSelectedSiteChanged(Site? value) => ApplyFilter();

        public ApartmentsViewModel(IApartmentService apartmentService, ISiteService siteService)
        {
            _apartmentService = apartmentService;
            _siteService = siteService;
            Title = "Apartman Yönetimi";
        }

        public void SetCurrentUser(User user)
        {
            _currentUser = user;
            _ = LoadDataAsync();
        }

        public async Task LoadDataAsync()
        {
            try
            {
                IsBusy = true;
                Apartments.Clear();
                SiteList.Clear();

                if (_currentUser == null) return;

                // Siteleri yükle
                IEnumerable<Site> sites;
                IEnumerable<Apartment> apartments;

                if (_currentUser.Role == UserRole.SuperAdmin)
                {
                    // SuperAdmin tüm verileri görebilir
                    sites = await _siteService.GetAllAsync();
                    apartments = await _apartmentService.GetAllAsync();
                }
                else if (_currentUser.Role == UserRole.Admin)
                {
                    // Admin sadece kendi şirketinin verilerini görebilir
                    sites = await _siteService.GetByCompanyIdAsync(_currentUser.CompanyId);
                    apartments = await _apartmentService.GetByCompanyIdAsync(_currentUser.CompanyId);
                }
                else
                {
                    // Normal kullanıcı ve Emlakçı sadece kendi verilerini görebilir
                    sites = await _siteService.GetByUserIdAsync(_currentUser.Id);
                    apartments = await _apartmentService.GetByUserIdAsync(_currentUser.Id);
                }

                // Site listesini doldur
                foreach (var site in sites.OrderBy(s => s.Name))
                {
                    SiteList.Add(site);
                }

                // Apartman listesini doldur
                foreach (var apartment in apartments.OrderBy(a => a.Name))
                {
                    Apartments.Add(apartment);
                }

                ApplyFilter();
            }
            catch (Exception ex)
            {
                // Hata durumunda boş liste
                Apartments.Clear();
                FilteredApartments.Clear();
                SiteList.Clear();
            }
            finally
            {
                IsBusy = false;
            }
        }

        private void ApplyFilter()
        {
            var query = Apartments.AsEnumerable();

            if (!string.IsNullOrWhiteSpace(SearchText))
                query = query.Where(a => a.Name.ToLower().Contains(SearchText.ToLower()));

            if (SelectedSite != null)
                query = query.Where(a => a.SiteId == SelectedSite.Id);

            var filtered = new ObservableCollection<Apartment>();
            foreach (var apartment in query.OrderBy(a => a.Name))
            {
                filtered.Add(apartment);
            }

            FilteredApartments = filtered;
        }

        [RelayCommand]
        private async Task AddApartment()
        {
            if (_currentUser == null) return;

            try
            {
                var apartmentService = Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetRequiredService<IApartmentService>(((App)App.Current).Services);
                var siteService = Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetRequiredService<ISiteService>(((App)App.Current).Services);

                var viewModel = new ViewModels.Dialogs.ApartmentDialogViewModel(apartmentService, siteService, _currentUser);
                var dialog = new Views.Dialogs.ApartmentDialog { DataContext = viewModel };

                if (Application.Current.MainWindow != null)
                {
                    dialog.Owner = Application.Current.MainWindow;
                }

                var result = dialog.ShowDialog();

                if (result == true && viewModel.DialogResult)
                {
                    await LoadDataAsync();
                    MessageBox.Show("Apartman başarıyla eklendi.", "Başarılı", MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Apartman ekleme hatası: {ex.Message}", "Hata", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        [RelayCommand]
        private async Task EditApartment(Apartment? apartment)
        {
            if (apartment == null || _currentUser == null) return;

            try
            {
                var apartmentService = Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetRequiredService<IApartmentService>(((App)App.Current).Services);
                var siteService = Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetRequiredService<ISiteService>(((App)App.Current).Services);

                var viewModel = new ViewModels.Dialogs.ApartmentDialogViewModel(apartmentService, siteService, _currentUser, apartment);
                var dialog = new Views.Dialogs.ApartmentDialog { DataContext = viewModel };

                if (Application.Current.MainWindow != null)
                {
                    dialog.Owner = Application.Current.MainWindow;
                }

                var result = dialog.ShowDialog();

                if (result == true && viewModel.DialogResult)
                {
                    await LoadDataAsync();
                    MessageBox.Show("Apartman başarıyla güncellendi.", "Başarılı", MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Apartman düzenleme hatası: {ex.Message}", "Hata", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        [RelayCommand]
        private async Task DeleteApartment(Apartment? apartment)
        {
            if (apartment == null) return;

            var result = MessageBox.Show($"{apartment.Name} apartmanını silmek istediğinize emin misiniz?",
                                       "Apartman Sil",
                                       MessageBoxButton.YesNo,
                                       MessageBoxImage.Warning);

            if (result == MessageBoxResult.Yes)
            {
                try
                {
                    IsBusy = true;
                    await _apartmentService.DeleteAsync(apartment.Id);
                    await LoadDataAsync();

                    MessageBox.Show("Apartman başarıyla silindi.", "Başarılı", MessageBoxButton.OK, MessageBoxImage.Information);
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"Apartman silinirken hata oluştu: {ex.Message}", "Hata", MessageBoxButton.OK, MessageBoxImage.Error);
                }
                finally
                {
                    IsBusy = false;
                }
            }
        }

        [RelayCommand]
        private void ClearFilters()
        {
            SearchText = string.Empty;
            SelectedSite = null;
            ApplyFilter();
        }
    }
}
