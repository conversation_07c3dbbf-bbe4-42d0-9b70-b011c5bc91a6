<UserControl x:Class="ApartmanYonetimSistemi.Views.DashboardView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">
    
    <ScrollViewer VerticalScrollBarVisibility="Auto">
        <Grid Margin="20">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
            </Grid.RowDefinitions>

            <!-- Ba<PERSON>lık -->
            <TextBlock Grid.Row="0" 
                       Text="Dashboard" 
                       Style="{StaticResource TitleStyle}"/>

            <!-- <PERSON><PERSON>t <PERSON> -->
            <Grid Grid.Row="1" Margin="0,0,0,30">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>

                <!-- Toplam Siteler -->
                <Border Grid.Column="0" Style="{StaticResource CardStyle}" Background="#E3F2FD">
                    <StackPanel>
                        <TextBlock Text="🏢" FontSize="32" HorizontalAlignment="Center" Margin="0,0,0,10"/>
                        <TextBlock Text="{Binding TotalSites}" 
                                   FontSize="28" 
                                   FontWeight="Bold" 
                                   HorizontalAlignment="Center" 
                                   Foreground="#1976D2"/>
                        <TextBlock Text="Toplam Site" 
                                   FontSize="14" 
                                   HorizontalAlignment="Center" 
                                   Foreground="#666666"/>
                    </StackPanel>
                </Border>

                <!-- Toplam Apartmanlar -->
                <Border Grid.Column="1" Style="{StaticResource CardStyle}" Background="#E8F5E8">
                    <StackPanel>
                        <TextBlock Text="🏠" FontSize="32" HorizontalAlignment="Center" Margin="0,0,0,10"/>
                        <TextBlock Text="{Binding TotalApartments}" 
                                   FontSize="28" 
                                   FontWeight="Bold" 
                                   HorizontalAlignment="Center" 
                                   Foreground="#388E3C"/>
                        <TextBlock Text="Toplam Apartman" 
                                   FontSize="14" 
                                   HorizontalAlignment="Center" 
                                   Foreground="#666666"/>
                    </StackPanel>
                </Border>

                <!-- Toplam Daireler -->
                <Border Grid.Column="2" Style="{StaticResource CardStyle}" Background="#FFF3E0">
                    <StackPanel>
                        <TextBlock Text="🚪" FontSize="32" HorizontalAlignment="Center" Margin="0,0,0,10"/>
                        <TextBlock Text="{Binding TotalUnits}" 
                                   FontSize="28" 
                                   FontWeight="Bold" 
                                   HorizontalAlignment="Center" 
                                   Foreground="#F57C00"/>
                        <TextBlock Text="Toplam Daire" 
                                   FontSize="14" 
                                   HorizontalAlignment="Center" 
                                   Foreground="#666666"/>
                    </StackPanel>
                </Border>

                <!-- Aktif Kiracılar -->
                <Border Grid.Column="3" Style="{StaticResource CardStyle}" Background="#FCE4EC">
                    <StackPanel>
                        <TextBlock Text="👥" FontSize="32" HorizontalAlignment="Center" Margin="0,0,0,10"/>
                        <TextBlock Text="{Binding ActiveTenants}" 
                                   FontSize="28" 
                                   FontWeight="Bold" 
                                   HorizontalAlignment="Center" 
                                   Foreground="#C2185B"/>
                        <TextBlock Text="Aktif Kiracı" 
                                   FontSize="14" 
                                   HorizontalAlignment="Center" 
                                   Foreground="#666666"/>
                    </StackPanel>
                </Border>
            </Grid>

            <!-- Hızlı İşlemler -->
            <Border Grid.Row="2" Style="{StaticResource CardStyle}" Margin="0,0,0,20">
                <StackPanel>
                    <TextBlock Text="Hızlı İşlemler" 
                               Style="{StaticResource SubtitleStyle}"/>
                    
                    <WrapPanel Orientation="Horizontal">
                        <Button Content="➕ Yeni Site Ekle" 
                                Style="{StaticResource PrimaryButton}" 
                                Margin="0,0,10,10"
                                Command="{Binding AddSiteCommand}"/>
                        
                        <Button Content="🏠 Yeni Apartman Ekle" 
                                Style="{StaticResource PrimaryButton}" 
                                Margin="0,0,10,10"
                                Command="{Binding AddApartmentCommand}"/>
                        
                        <Button Content="👤 Yeni Kiracı Ekle" 
                                Style="{StaticResource PrimaryButton}" 
                                Margin="0,0,10,10"
                                Command="{Binding AddTenantCommand}"/>
                        
                        <Button Content="💰 Ödeme Kaydet"
                                Style="{StaticResource SecondaryButton}"
                                Margin="0,0,10,10"
                                Command="{Binding AddPaymentCommand}"/>

                        <Button Content="👥 Kullanıcı Yönetimi"
                                Style="{StaticResource PrimaryButton}"
                                Margin="0,0,10,10"
                                Command="{Binding ManageUsersCommand}"
                                Visibility="{Binding CanShowUserManagement, Converter={StaticResource BooleanToVisibilityConverter}}"/>
                    </WrapPanel>
                </StackPanel>
            </Border>

            <!-- Hoş Geldin Mesajı -->
            <Border Grid.Row="3" Style="{StaticResource CardStyle}">
                <StackPanel>
                    <TextBlock Text="Hoş Geldiniz!" 
                               Style="{StaticResource SubtitleStyle}"/>
                    
                    <TextBlock Text="Apartman Yönetim Sistemi'ne hoş geldiniz. Yukarıdaki hızlı işlemler menüsünden başlayabilir veya sol menüden istediğiniz bölüme geçebilirsiniz."
                               FontSize="14" 
                               Foreground="#666666"
                               TextWrapping="Wrap"
                               Margin="0,0,0,20"/>
                               
                    <TextBlock Text="💡 İpucu: Admin veya SuperAdmin hesabıyla giriş yaptıysanız 'Kullanıcı Yönetimi' butonunu görebilirsiniz."
                               FontSize="12" 
                               Foreground="#999999"
                               FontStyle="Italic"
                               TextWrapping="Wrap"/>
                </StackPanel>
            </Border>
        </Grid>
    </ScrollViewer>
</UserControl>
