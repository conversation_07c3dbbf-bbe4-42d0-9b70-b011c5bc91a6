using System.Windows;
using ApartmanYonetimSistemi.ViewModels;
using Microsoft.Extensions.DependencyInjection;

namespace ApartmanYonetimSistemi.Views
{
    /// <summary>
    /// LoginView.xaml etkileşim mantığı
    /// </summary>
    public partial class LoginView : Window
    {
        public LoginView()
        {
            InitializeComponent();

            // ViewModel'i DI container'dan al
            var app = (App)Application.Current;
            DataContext = app.Services.GetRequiredService<LoginViewModel>();
        }

        private void Window_Loaded(object sender, RoutedEventArgs e)
        {
            // Pencere yüklendiğinde e-posta textbox'ına odaklan
            if (DataContext is LoginViewModel viewModel)
            {
                // E-posta alanına odaklan
                Dispatcher.BeginInvoke(new System.Action(() =>
                {
                    var emailTextBox = FindName("EmailTextBox") as System.Windows.Controls.TextBox;
                    emailTextBox?.Focus();
                }));
            }
        }

        private void PasswordBox_KeyDown(object sender, System.Windows.Input.KeyEventArgs e)
        {
            // Enter tuşuna basıldığında giriş yap
            if (e.Key == System.Windows.Input.Key.Enter)
            {
                if (DataContext is LoginViewModel viewModel)
                {
                    if (viewModel.LoginCommand.CanExecute(PasswordBox))
                    {
                        viewModel.LoginCommand.Execute(PasswordBox);
                    }
                }
            }
        }

        private void EmailTextBox_KeyDown(object sender, System.Windows.Input.KeyEventArgs e)
        {
            // Enter tuşuna basıldığında giriş yap
            if (e.Key == System.Windows.Input.Key.Enter)
            {
                if (DataContext is LoginViewModel viewModel)
                {
                    if (viewModel.LoginCommand.CanExecute(PasswordBox))
                    {
                        viewModel.LoginCommand.Execute(PasswordBox);
                    }
                }
            }
        }
    }
}
