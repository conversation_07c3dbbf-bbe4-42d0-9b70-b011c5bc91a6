using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using ApartmanYonetimSistemi.Services;
using ApartmanYonetimSistemi.Models;

namespace ApartmanYonetimSistemi.ViewModels
{
    public partial class DashboardViewModel : BaseViewModel
    {
        private readonly IApartmanService _apartmanService;

        [ObservableProperty]
        private int _totalSites = 0;

        [ObservableProperty]
        private int _totalApartments = 0;

        [ObservableProperty]
        private int _totalUnits = 0;

        [ObservableProperty]
        private int _activeTenants = 0;

        public DashboardViewModel(IApartmanService apartmanService)
        {
            _apartmanService = apartmanService;
            Title = "Dashboard";
            LoadDashboardDataAsync();
        }

        private async Task LoadDashboardDataAsync()
        {
            try
            {
                IsBusy = true;

                var sites = await _apartmanService.GetSitesAsync();
                TotalSites = sites.Count;

                var apartments = new List<Apartment>();
                foreach (var site in sites)
                {
                    var siteApartments = await _apartmanService.GetApartmentsBySiteIdAsync(site.Id);
                    apartments.AddRange(siteApartments);
                }
                TotalApartments = apartments.Count;

                var units = new List<Unit>();
                foreach (var apartment in apartments)
                {
                    var apartmentUnits = await _apartmanService.GetUnitsByApartmentIdAsync(apartment.Id);
                    units.AddRange(apartmentUnits);
                }
                TotalUnits = units.Count;

                var tenants = await _apartmanService.GetTenantsAsync();
                ActiveTenants = tenants.Count(t => t.Status == TenantStatus.Active);
            }
            catch (Exception ex)
            {
                // Log error
                System.Diagnostics.Debug.WriteLine($"Dashboard yükleme hatası: {ex.Message}");
            }
            finally
            {
                IsBusy = false;
            }
        }
    }
}
