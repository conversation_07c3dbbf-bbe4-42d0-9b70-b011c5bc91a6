<Window x:Class="ApartmanYonetimSistemi.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:ApartmanYonetimSistemi"
        mc:Ignorable="d"
        Title="Apartman Yönetim Sistemi" Height="800" Width="1200"
        WindowStartupLocation="CenterScreen">

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <Border Grid.Row="0" Background="#2196F3" Padding="10">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <StackPanel Grid.Column="0" Orientation="Horizontal">
                    <TextBlock Text="🏢" FontSize="24" Foreground="White" VerticalAlignment="Center" Margin="0,0,10,0"/>
                    <TextBlock Text="Apartman Yönetim Sistemi" FontSize="20" FontWeight="Bold" Foreground="White" VerticalAlignment="Center"/>
                </StackPanel>

                <StackPanel Grid.Column="1" Orientation="Horizontal">
                    <TextBlock x:Name="UserInfoText" Text="Kullanıcı: Admin" Foreground="White" VerticalAlignment="Center" Margin="0,0,15,0"/>
                    <Button x:Name="UserManagementButton" Content="👥 Yönetim" Click="UserManagementButton_Click"
                            Background="#FF9800" Foreground="White"
                            Padding="15,5" BorderThickness="0"
                            Cursor="Hand" Margin="0,0,10,0"
                            Visibility="Collapsed"/>
                    <Button Content="Çıkış" Click="ExitButton_Click"
                            Background="#F44336" Foreground="White"
                            Padding="15,5" BorderThickness="0"
                            Cursor="Hand"/>
                </StackPanel>
            </Grid>
        </Border>

        <!-- Main Content -->
        <Grid Grid.Row="1" Background="#F5F5F5">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="250"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>

            <!-- Sidebar -->
            <Border Grid.Column="0" Background="White" BorderBrush="#E0E0E0" BorderThickness="0,0,1,0">
                <StackPanel Margin="20">
                    <TextBlock Text="Menü"
                               FontSize="18"
                               FontWeight="Bold"
                               Foreground="#333333"
                               Margin="0,0,0,20"/>

                    <!-- Menu Items -->
                    <Button Content="📊 Dashboard"
                            Style="{StaticResource SecondaryButton}"
                            HorizontalAlignment="Stretch"
                            Margin="0,0,0,10"
                            Click="DashboardButton_Click"/>

                    <Button Content="🏢 Siteler"
                            Style="{StaticResource SecondaryButton}"
                            HorizontalAlignment="Stretch"
                            Margin="0,0,0,10"
                            Click="SitesButton_Click"/>

                    <Button Content="🏠 Apartmanlar"
                            Style="{StaticResource SecondaryButton}"
                            HorizontalAlignment="Stretch"
                            Margin="0,0,0,10"
                            Click="ApartmentsButton_Click"/>

                    <Button Content="🚪 Daireler"
                            Style="{StaticResource SecondaryButton}"
                            HorizontalAlignment="Stretch"
                            Margin="0,0,0,10"
                            Click="UnitsButton_Click"/>

                    <Button Content="👥 Kiracılar"
                            Style="{StaticResource SecondaryButton}"
                            HorizontalAlignment="Stretch"
                            Margin="0,0,0,10"
                            Click="TenantsButton_Click"/>

                    <Button Content="💰 Ödemeler"
                            Style="{StaticResource SecondaryButton}"
                            HorizontalAlignment="Stretch"
                            Margin="0,0,0,10"
                            Click="PaymentsButton_Click"/>

                    <Button Content="👥 Kullanıcılar"
                            Style="{StaticResource SecondaryButton}"
                            HorizontalAlignment="Stretch"
                            Margin="0,0,0,10"
                            Click="UsersButton_Click"/>
                </StackPanel>
            </Border>

            <!-- Content Area -->
            <ContentControl Grid.Column="1" x:Name="ContentArea" Background="#F5F5F5"/>
        </Grid>

        <!-- Footer -->
        <Border Grid.Row="2" Background="#37474F" Padding="10">
            <TextBlock Text="© 2025 Apartman Yönetim Sistemi - Tüm hakları saklıdır."
                       Foreground="White" HorizontalAlignment="Center"/>
        </Border>
    </Grid>


</Window>
