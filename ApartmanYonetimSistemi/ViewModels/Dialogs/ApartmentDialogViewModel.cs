using System.Collections.ObjectModel;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using ApartmanYonetimSistemi.Models;
using ApartmanYonetimSistemi.Services;
using System.Threading.Tasks;
using System.Windows;

namespace ApartmanYonetimSistemi.ViewModels.Dialogs
{
    public partial class ApartmentDialogViewModel : BaseViewModel
    {
        private readonly IApartmentService _apartmentService;
        private readonly ISiteService _siteService;
        private readonly User _currentUser;
        private Apartment? _editingApartment;

        [ObservableProperty]
        private string _apartmentName = string.Empty;

        [ObservableProperty]
        private string? _selectedSiteId;

        [ObservableProperty]
        private int _floorCount = 1;

        [ObservableProperty]
        private int _unitCount = 1;

        [ObservableProperty]
        private int? _buildYear;

        [ObservableProperty]
        private string _description = string.Empty;

        [ObservableProperty]
        private ObservableCollection<Site> _sites = new();

        [ObservableProperty]
        private string _saveButtonText = "Kaydet";

        // Validation Errors
        [ObservableProperty]
        private string _apartmentNameError = string.Empty;

        [ObservableProperty]
        private string _siteError = string.Empty;

        public bool DialogResult { get; private set; }

        public ApartmentDialogViewModel(IApartmentService apartmentService, ISiteService siteService, User currentUser, Apartment? editingApartment = null)
        {
            _apartmentService = apartmentService;
            _siteService = siteService;
            _currentUser = currentUser;
            _editingApartment = editingApartment;

            Title = editingApartment == null ? "Yeni Apartman Ekle" : "Apartman Düzenle";
            SaveButtonText = editingApartment == null ? "Kaydet" : "Güncelle";

            if (editingApartment != null)
            {
                LoadApartmentData();
            }

            _ = LoadSitesAsync();
        }

        private void LoadApartmentData()
        {
            if (_editingApartment == null) return;

            ApartmentName = _editingApartment.Name;
            SelectedSiteId = _editingApartment.SiteId;
            FloorCount = _editingApartment.FloorCount;
            UnitCount = _editingApartment.UnitCount;
            BuildYear = _editingApartment.BuildYear;
            Description = _editingApartment.Description ?? string.Empty;
        }

        private async Task LoadSitesAsync()
        {
            try
            {
                IEnumerable<Site> siteList;

                if (_currentUser.Role == UserRole.SuperAdmin)
                {
                    siteList = await _siteService.GetAllAsync();
                }
                else if (_currentUser.Role == UserRole.Admin)
                {
                    siteList = await _siteService.GetByCompanyIdAsync(_currentUser.CompanyId);
                }
                else
                {
                    siteList = await _siteService.GetByUserIdAsync(_currentUser.Id);
                }

                Sites.Clear();
                foreach (var site in siteList.OrderBy(s => s.Name))
                {
                    Sites.Add(site);
                }
            }
            catch (Exception ex)
            {
                // Handle error
            }
        }

        [RelayCommand]
        private async Task Save()
        {
            if (!ValidateForm()) return;

            try
            {
                IsBusy = true;

                var apartment = _editingApartment ?? new Apartment { Id = Guid.NewGuid().ToString() };
                
                apartment.Name = ApartmentName.Trim();
                apartment.SiteId = SelectedSiteId!;
                apartment.FloorCount = FloorCount;
                apartment.UnitCount = UnitCount;
                apartment.BuildYear = BuildYear ?? 0;
                apartment.Description = string.IsNullOrWhiteSpace(Description) ? null : Description.Trim();
                apartment.UserId = _currentUser.Id;

                if (_editingApartment == null)
                {
                    await _apartmentService.CreateAsync(apartment);
                }
                else
                {
                    await _apartmentService.UpdateAsync(apartment);
                }

                DialogResult = true;
                
                // Close dialog
                if (Application.Current.MainWindow?.OwnedWindows.Count > 0)
                {
                    var dialog = Application.Current.MainWindow.OwnedWindows[^1];
                    dialog.DialogResult = true;
                    dialog.Close();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Apartman kaydedilirken hata oluştu: {ex.Message}", "Hata", MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                IsBusy = false;
            }
        }

        [RelayCommand]
        private void Cancel()
        {
            DialogResult = false;
            
            // Close dialog
            if (Application.Current.MainWindow?.OwnedWindows.Count > 0)
            {
                var dialog = Application.Current.MainWindow.OwnedWindows[^1];
                dialog.DialogResult = false;
                dialog.Close();
            }
        }

        private bool ValidateForm()
        {
            bool isValid = true;

            // Apartment Name validation
            if (string.IsNullOrWhiteSpace(ApartmentName))
            {
                ApartmentNameError = "Apartman adı gereklidir";
                isValid = false;
            }
            else if (ApartmentName.Trim().Length < 2)
            {
                ApartmentNameError = "Apartman adı en az 2 karakter olmalıdır";
                isValid = false;
            }
            else
            {
                ApartmentNameError = string.Empty;
            }

            // Site validation
            if (string.IsNullOrWhiteSpace(SelectedSiteId))
            {
                SiteError = "Site seçimi gereklidir";
                isValid = false;
            }
            else
            {
                SiteError = string.Empty;
            }

            return isValid;
        }
    }
}
