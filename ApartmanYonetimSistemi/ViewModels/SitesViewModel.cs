using System.Collections.ObjectModel;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using ApartmanYonetimSistemi.Models;
using ApartmanYonetimSistemi.Services;
using ApartmanYonetimSistemi.Views.Dialogs;
using ApartmanYonetimSistemi.ViewModels.Dialogs;
using System.Threading.Tasks;
using System.Windows;
using System.Linq;
using System.Globalization;
using Microsoft.Extensions.DependencyInjection;

namespace ApartmanYonetimSistemi.ViewModels
{
    public partial class SitesViewModel : BaseViewModel
    {
        private readonly ISiteService _siteService;
        private readonly IServiceProvider _serviceProvider;

        [ObservableProperty]
        private ObservableCollection<Site> _sites = new();

        [ObservableProperty]
        private ObservableCollection<Site> _filteredSites = new();

        [ObservableProperty]
        private string _searchText = string.Empty;

        [ObservableProperty]
        private string? _selectedCity;

        [ObservableProperty]
        private string? _selectedDistrict;

        [ObservableProperty]
        private ObservableCollection<string> _cityList = new();

        [ObservableProperty]
        private ObservableCollection<string> _districtList = new();

        private User? _currentUser;

        partial void OnSearchTextChanged(string value) => ApplyFilter();
        partial void OnSelectedCityChanged(string? value)
        {
            UpdateDistrictList();
            ApplyFilter();
        }
        partial void OnSelectedDistrictChanged(string? value) => ApplyFilter();

        public SitesViewModel(ISiteService siteService, IServiceProvider serviceProvider)
        {
            _siteService = siteService;
            _serviceProvider = serviceProvider;
            Title = "Site Yönetimi";
        }

        public void SetCurrentUser(User user)
        {
            _currentUser = user;
            _ = LoadSitesAsync();
        }

        public async Task LoadSitesAsync()
        {
            try
            {
                IsBusy = true;
                Sites.Clear();

                if (_currentUser == null) return;

                IEnumerable<Site> siteList;

                // Kullanıcı rolüne göre site listesi yükleme
                if (_currentUser.Role == UserRole.SuperAdmin)
                {
                    // SuperAdmin tüm siteleri görebilir
                    siteList = await _siteService.GetAllAsync();
                }
                else if (_currentUser.Role == UserRole.Admin)
                {
                    // Admin sadece kendi şirketinin sitelerini görebilir
                    siteList = await _siteService.GetByCompanyIdAsync(_currentUser.CompanyId);
                }
                else
                {
                    // Normal kullanıcı ve Emlakçı sadece kendi sitelerini görebilir
                    siteList = await _siteService.GetByUserIdAsync(_currentUser.Id);
                }

                foreach (var site in siteList)
                {
                    Sites.Add(site);
                }

                UpdateCityList();
                ApplyFilter();
            }
            catch (Exception ex)
            {
                // Hata durumunda boş liste
                Sites.Clear();
                FilteredSites.Clear();
            }
            finally
            {
                IsBusy = false;
            }
        }

        private void UpdateCityList()
        {
            CityList.Clear();
            var culture = CultureInfo.CurrentCulture;
            var cities = Sites.Select(s => ToTitle(s.City ?? "", culture))
                             .Distinct()
                             .Where(c => !string.IsNullOrWhiteSpace(c))
                             .OrderBy(c => c);
            
            foreach (var city in cities)
                CityList.Add(city);
        }

        private void UpdateDistrictList()
        {
            DistrictList.Clear();
            if (!string.IsNullOrWhiteSpace(SelectedCity))
            {
                var culture = CultureInfo.CurrentCulture;
                var districts = Sites.Where(s => ToTitle(s.City ?? "", culture) == SelectedCity)
                    .Select(s => ToTitle(s.District ?? "", culture))
                    .Distinct()
                    .Where(d => !string.IsNullOrWhiteSpace(d))
                    .OrderBy(d => d);
                
                foreach (var district in districts)
                    DistrictList.Add(district);
            }
        }

        private void ApplyFilter()
        {
            var query = Sites.AsEnumerable();
            
            if (!string.IsNullOrWhiteSpace(SearchText))
                query = query.Where(s => s.Name.ToLower().Contains(SearchText.ToLower()));
            
            if (!string.IsNullOrWhiteSpace(SelectedCity))
                query = query.Where(s => s.City == SelectedCity);
            
            if (!string.IsNullOrWhiteSpace(SelectedDistrict))
                query = query.Where(s => s.District == SelectedDistrict);

            var culture = CultureInfo.CurrentCulture;
            var filtered = new ObservableCollection<Site>();
            
            foreach (var site in query.OrderBy(s => s.Name))
            {
                // Başlık formatında göster
                site.Name = ToTitle(site.Name, culture);
                site.District = ToTitle(site.District ?? "", culture);
                site.City = ToTitle(site.City ?? "", culture);
                filtered.Add(site);
            }
            
            FilteredSites = filtered;
        }

        private string ToTitle(string text, CultureInfo culture)
        {
            if (string.IsNullOrWhiteSpace(text)) return text;
            text = text.Trim();
            return char.ToUpper(text[0], culture) + (text.Length > 1 ? text.Substring(1) : "");
        }

        [RelayCommand]
        private async Task AddSite()
        {
            if (_currentUser == null) return;

            try
            {
                var siteService = _serviceProvider.GetRequiredService<ISiteService>();
                var companyService = _serviceProvider.GetRequiredService<ICompanyService>();

                var viewModel = new SiteDialogViewModel(siteService, companyService, _currentUser);
                var dialog = new SiteDialog { DataContext = viewModel };

                if (Application.Current.MainWindow != null)
                {
                    dialog.Owner = Application.Current.MainWindow;
                }

                var result = dialog.ShowDialog();

                if (result == true && viewModel.DialogResult)
                {
                    await LoadSitesAsync();
                    MessageBox.Show("Site başarıyla eklendi.", "Başarılı", MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Site ekleme hatası: {ex.Message}", "Hata", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        [RelayCommand]
        private async Task EditSite(Site? site)
        {
            if (site == null || _currentUser == null) return;

            try
            {
                var siteService = Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetRequiredService<ISiteService>(((App)App.Current).Services);
                var companyService = Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetRequiredService<ICompanyService>(((App)App.Current).Services);

                var viewModel = new ViewModels.Dialogs.SiteDialogViewModel(siteService, companyService, _currentUser, site);
                var dialog = new Views.Dialogs.SiteDialog { DataContext = viewModel };

                if (Application.Current.MainWindow != null)
                {
                    dialog.Owner = Application.Current.MainWindow;
                }

                var result = dialog.ShowDialog();

                if (result == true && viewModel.DialogResult)
                {
                    await LoadSitesAsync();
                    MessageBox.Show("Site başarıyla güncellendi.", "Başarılı", MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Site düzenleme hatası: {ex.Message}", "Hata", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        [RelayCommand]
        private async Task DeleteSite(Site? site)
        {
            if (site == null) return;
            
            var result = MessageBox.Show($"{site.Name} sitesini silmek istediğinize emin misiniz?", 
                                       "Site Sil", 
                                       MessageBoxButton.YesNo, 
                                       MessageBoxImage.Warning);
            
            if (result == MessageBoxResult.Yes)
            {
                try
                {
                    IsBusy = true;
                    await _siteService.DeleteAsync(site.Id);
                    await LoadSitesAsync();
                    
                    MessageBox.Show("Site başarıyla silindi.", "Başarılı", MessageBoxButton.OK, MessageBoxImage.Information);
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"Site silinirken hata oluştu: {ex.Message}", "Hata", MessageBoxButton.OK, MessageBoxImage.Error);
                }
                finally
                {
                    IsBusy = false;
                }
            }
        }

        [RelayCommand]
        private void ClearFilters()
        {
            SearchText = string.Empty;
            SelectedCity = null;
            SelectedDistrict = null;
            ApplyFilter();
        }
    }
}
