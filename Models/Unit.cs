using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace ApartmanYonetimSistemi.Models
{
    public class Unit : BaseEntity
    {
        [Required]
        [StringLength(50)]
        public string ApartmentId { get; set; } = string.Empty;

        [Required]
        [StringLength(10)]
        public string UnitNumber { get; set; } = string.Empty;

        public int Floor { get; set; } = 0;

        [Required]
        public UnitType Type { get; set; } = UnitType.Apartment;

        [StringLength(20)]
        public string RoomCount { get; set; } = string.Empty; // "2+1", "3+1" gibi

        [Column(TypeName = "decimal(8,2)")]
        public decimal Size { get; set; } = 0; // m²

        [Column(TypeName = "decimal(8,2)")]
        public decimal BalconyArea { get; set; } = 0; // m²

        public bool HasBalcony { get; set; } = false;

        public bool HasGarden { get; set; } = false;

        public int ParkingSpots { get; set; } = 0;

        [StringLength(50)]
        public string? CurrentTenantId { get; set; }

        [StringLength(50)]
        public string? OwnerId { get; set; }

        public bool IsRented { get; set; } = false;

        [Column(TypeName = "decimal(18,2)")]
        public decimal RentAmount { get; set; } = 0;

        [Column(TypeName = "decimal(18,2)")]
        public decimal MaintenanceFee { get; set; } = 0;

        [Column(TypeName = "decimal(18,2)")]
        public decimal Deposit { get; set; } = 0;

        public DateTime? LeaseStartDate { get; set; }

        public DateTime? LeaseEndDate { get; set; }

        [Required]
        public UnitStatus Status { get; set; } = UnitStatus.Available;

        [StringLength(1000)]
        public string Description { get; set; } = string.Empty;

        [StringLength(50)]
        public string CompanyId { get; set; } = string.Empty;

        // Navigation properties
        public virtual Apartment Apartment { get; set; } = null!;
        public virtual Tenant? CurrentTenant { get; set; }
        public virtual ICollection<Payment> Payments { get; set; } = new List<Payment>();

        // Computed properties
        [NotMapped]
        public string DisplayName => $"Daire {UnitNumber}";

        [NotMapped]
        public decimal TotalArea => Size + BalconyArea;

        [NotMapped]
        public bool IsLeaseActive => LeaseEndDate.HasValue && LeaseEndDate.Value > DateTime.UtcNow;
    }
}
