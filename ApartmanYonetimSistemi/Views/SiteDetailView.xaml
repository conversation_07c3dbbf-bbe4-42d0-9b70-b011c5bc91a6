<UserControl x:Class="ApartmanYonetimSistemi.Views.SiteDetailView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:converters="clr-namespace:ApartmanYonetimSistemi.Converters">

    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <Border Grid.Row="0" Style="{StaticResource CardStyle}" Margin="0,0,0,10">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <StackPanel Grid.Column="0">
                    <TextBlock Text="{Binding Site.Name, StringFormat='🏢 {0} - Detaylar'}" Style="{StaticResource TitleStyle}"/>
                    <TextBlock Text="Site bilgileri ve apartmanları" 
                               Style="{StaticResource SubtitleStyle}" Opacity="0.7"/>
                </StackPanel>

                <StackPanel Grid.Column="1" Orientation="Horizontal">
                    <Button Content="✏️ Düzenle" 
                            Style="{StaticResource PrimaryButton}"
                            Command="{Binding EditSiteCommand}"
                            Margin="0,0,10,0"/>
                    <Button Content="🔙 Geri" 
                            Style="{StaticResource SecondaryButton}"
                            Command="{Binding GoBackCommand}"/>
                </StackPanel>
            </Grid>
        </Border>

        <!-- Site Info -->
        <Border Grid.Row="1" Style="{StaticResource CardStyle}" Margin="0,0,0,10">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>

                <!-- Left Column -->
                <StackPanel Grid.Column="0" Margin="0,0,20,0">
                    <TextBlock Text="Site Bilgileri" Style="{StaticResource SubtitleStyle}" Margin="0,0,0,15"/>
                    
                    <Grid Margin="0,0,0,10">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="100"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>
                        <TextBlock Grid.Column="0" Text="Site Adı:" FontWeight="SemiBold"/>
                        <TextBlock Grid.Column="1" Text="{Binding Site.Name}"/>
                    </Grid>

                    <Grid Margin="0,0,0,10">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="100"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>
                        <TextBlock Grid.Column="0" Text="Adres:" FontWeight="SemiBold"/>
                        <TextBlock Grid.Column="1" Text="{Binding Site.Address}" TextWrapping="Wrap"/>
                    </Grid>

                    <Grid Margin="0,0,0,10">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="100"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>
                        <TextBlock Grid.Column="0" Text="Şehir:" FontWeight="SemiBold"/>
                        <TextBlock Grid.Column="1" Text="{Binding Site.City}"/>
                    </Grid>

                    <Grid Margin="0,0,0,10">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="100"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>
                        <TextBlock Grid.Column="0" Text="İlçe:" FontWeight="SemiBold"/>
                        <TextBlock Grid.Column="1" Text="{Binding Site.District}"/>
                    </Grid>
                </StackPanel>

                <!-- Right Column -->
                <StackPanel Grid.Column="1">
                    <TextBlock Text="İstatistikler" Style="{StaticResource SubtitleStyle}" Margin="0,0,0,15"/>
                    
                    <Grid Margin="0,0,0,10">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="120"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>
                        <TextBlock Grid.Column="0" Text="Apartman Sayısı:" FontWeight="SemiBold"/>
                        <TextBlock Grid.Column="1" Text="{Binding ApartmentCount}"/>
                    </Grid>

                    <Grid Margin="0,0,0,10">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="120"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>
                        <TextBlock Grid.Column="0" Text="Toplam Daire:" FontWeight="SemiBold"/>
                        <TextBlock Grid.Column="1" Text="{Binding TotalUnits}"/>
                    </Grid>

                    <Grid Margin="0,0,0,10">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="120"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>
                        <TextBlock Grid.Column="0" Text="Dolu Daire:" FontWeight="SemiBold"/>
                        <TextBlock Grid.Column="1" Text="{Binding OccupiedUnits}"/>
                    </Grid>

                    <Grid Margin="0,0,0,10">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="120"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>
                        <TextBlock Grid.Column="0" Text="Doluluk Oranı:" FontWeight="SemiBold"/>
                        <TextBlock Grid.Column="1" Text="{Binding OccupancyRate, StringFormat={}{0:P0}}"/>
                    </Grid>
                </StackPanel>
            </Grid>
        </Border>

        <!-- Apartments List -->
        <Border Grid.Row="2" Style="{StaticResource CardStyle}">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                </Grid.RowDefinitions>

                <!-- List Header -->
                <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="0,0,0,10">
                    <TextBlock Text="Apartmanlar" Style="{StaticResource SubtitleStyle}"/>
                    <Button Content="➕ Yeni Apartman" 
                            Style="{StaticResource PrimaryButton}"
                            Command="{Binding AddApartmentCommand}"
                            Margin="20,0,0,0" Padding="10,5"/>
                </StackPanel>

                <!-- Loading Indicator -->
                <ProgressBar Grid.Row="1" IsIndeterminate="True" 
                             Visibility="{Binding IsBusy, Converter={x:Static converters:BooleanToVisibilityConverter.Instance}}"
                             Height="4" VerticalAlignment="Top"/>

                <!-- Data Grid -->
                <DataGrid Grid.Row="1" 
                          ItemsSource="{Binding Apartments}"
                          SelectedItem="{Binding SelectedApartment}"
                          AutoGenerateColumns="False"
                          CanUserAddRows="False"
                          CanUserDeleteRows="False"
                          IsReadOnly="True"
                          GridLinesVisibility="Horizontal"
                          HeadersVisibility="Column"
                          SelectionMode="Single"
                          AlternatingRowBackground="#F9F9F9">

                    <DataGrid.Columns>
                        <!-- Apartment Name -->
                        <DataGridTextColumn Header="Apartman Adı" Binding="{Binding Name}" Width="150"/>

                        <!-- Floor Count -->
                        <DataGridTextColumn Header="Kat Sayısı" Binding="{Binding FloorCount}" Width="80"/>

                        <!-- Unit Count -->
                        <DataGridTextColumn Header="Daire Sayısı" Binding="{Binding UnitCount}" Width="80"/>

                        <!-- Build Year -->
                        <DataGridTextColumn Header="Yapım Yılı" Binding="{Binding BuildYear}" Width="80"/>

                        <!-- Description -->
                        <DataGridTextColumn Header="Açıklama" Binding="{Binding Description}" Width="200"/>

                        <!-- Actions -->
                        <DataGridTemplateColumn Header="İşlemler" Width="150">
                            <DataGridTemplateColumn.CellTemplate>
                                <DataTemplate>
                                    <StackPanel Orientation="Horizontal">
                                        <Button Content="👁️" 
                                                Command="{Binding DataContext.ViewApartmentDetailsCommand, RelativeSource={RelativeSource AncestorType=UserControl}}"
                                                CommandParameter="{Binding}"
                                                Style="{StaticResource SecondaryButton}"
                                                Padding="8,4" Margin="0,0,5,0"
                                                ToolTip="Detayları Görüntüle"/>
                                        <Button Content="✏️" 
                                                Command="{Binding DataContext.EditApartmentCommand, RelativeSource={RelativeSource AncestorType=UserControl}}"
                                                CommandParameter="{Binding}"
                                                Style="{StaticResource SecondaryButton}"
                                                Padding="8,4"
                                                ToolTip="Düzenle"/>
                                    </StackPanel>
                                </DataTemplate>
                            </DataGridTemplateColumn.CellTemplate>
                        </DataGridTemplateColumn>
                    </DataGrid.Columns>
                </DataGrid>

                <!-- Empty State -->
                <StackPanel Grid.Row="1"
                            Visibility="{Binding Apartments.Count, Converter={x:Static converters:EmptyStateConverter.Instance}}"
                            VerticalAlignment="Center" HorizontalAlignment="Center">
                    <TextBlock Text="🏠" FontSize="48" HorizontalAlignment="Center" Opacity="0.3"/>
                    <TextBlock Text="Bu sitede henüz apartman eklenmemiş" 
                               FontSize="16" HorizontalAlignment="Center" 
                               Margin="0,10,0,5" Opacity="0.6"/>
                    <TextBlock Text="İlk apartmanı eklemek için 'Yeni Apartman' butonuna tıklayın" 
                               FontSize="12" HorizontalAlignment="Center" 
                               Opacity="0.5"/>
                </StackPanel>
            </Grid>
        </Border>
    </Grid>
</UserControl>
