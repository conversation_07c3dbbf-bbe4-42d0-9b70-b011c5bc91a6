<UserControl x:Class="ApartmanYonetimSistemi.Views.ApartmentsView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">
    <UserControl.Resources>
        <Style x:Key="ModernGridViewHeader" TargetType="GridViewColumnHeader">
            <Setter Property="Background" Value="#F5F7FA"/>
            <Setter Property="Foreground" Value="#222"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="FontSize" Value="15"/>
            <Setter Property="Padding" Value="8,4"/>
            <Setter Property="BorderBrush" Value="#E0E0E0"/>
            <Setter Property="BorderThickness" Value="0,0,0,2"/>
            <Setter Property="HorizontalContentAlignment" Value="Center"/>
            <Setter Property="VerticalContentAlignment" Value="Center"/>
            <Setter Property="SnapsToDevicePixels" Value="True"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="GridViewColumnHeader">
                        <Border Background="{TemplateBinding Background}" BorderBrush="{TemplateBinding BorderBrush}" BorderThickness="{TemplateBinding BorderThickness}">
                            <ContentPresenter HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}" VerticalAlignment="{TemplateBinding VerticalContentAlignment}"/>
                        </Border>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
        
        <Style x:Key="SoftButton" TargetType="Button">
            <Setter Property="Background" Value="#F3F6FA"/>
            <Setter Property="Foreground" Value="#333"/>
            <Setter Property="BorderBrush" Value="#E0E0E0"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="Padding" Value="0,8"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}" BorderBrush="{TemplateBinding BorderBrush}" BorderThickness="{TemplateBinding BorderThickness}" CornerRadius="8">
                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="#B3D1FF"/>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter Property="Background" Value="#90B8E6"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
        
        <Style x:Key="DangerButton" TargetType="Button">
            <Setter Property="Background" Value="#FFE5EC"/>
            <Setter Property="Foreground" Value="#B71C1C"/>
            <Setter Property="BorderBrush" Value="#FFB3C2"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="Padding" Value="0,8"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}" BorderBrush="{TemplateBinding BorderBrush}" BorderThickness="{TemplateBinding BorderThickness}" CornerRadius="8">
                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="#FFB3C2"/>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter Property="Background" Value="#FF8A9A"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
    </UserControl.Resources>
    
    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- Başlık ve Filtreler -->
        <StackPanel Grid.Row="0" Orientation="Horizontal" VerticalAlignment="Center" Margin="0,0,0,20">
            <TextBlock Text="Apartmanlar" FontSize="22" FontWeight="Bold" Foreground="#1976D2"/>
            <Button Content="+ Yeni Apartman Ekle" Command="{Binding AddApartmentCommand}" Style="{StaticResource PrimaryButton}" Margin="20,0,0,0" Height="32"/>
            
            <StackPanel Margin="30,0,0,0" Width="180">
                <TextBlock Text="Apartman Adı" FontWeight="SemiBold" FontSize="12" Foreground="#666" Margin="0,0,0,2"/>
                <TextBox Text="{Binding SearchText, UpdateSourceTrigger=PropertyChanged}" Height="32" Padding="8,0"/>
            </StackPanel>
            
            <StackPanel Margin="10,0,0,0" Width="140">
                <TextBlock Text="Site" FontWeight="SemiBold" FontSize="12" Foreground="#666" Margin="0,0,0,2"/>
                <ComboBox ItemsSource="{Binding SiteList}" 
                         SelectedItem="{Binding SelectedSite, UpdateSourceTrigger=PropertyChanged}" 
                         DisplayMemberPath="Name"
                         Height="32"/>
            </StackPanel>
            
            <Button Content="Filtreleri Temizle" Command="{Binding ClearFiltersCommand}" Style="{StaticResource SoftButton}" Margin="20,0,0,0" Height="32" MinWidth="120"/>
        </StackPanel>

        <!-- Açıklama -->
        <TextBlock Grid.Row="1" Text="Eklediğiniz apartmanlar aşağıda listelenmektedir." FontSize="14" Foreground="#666" Margin="0,0,0,10"/>

        <!-- Apartman Listesi -->
        <Border Grid.Row="2" Background="White" CornerRadius="10" Padding="15">
            <Border.Effect>
                <DropShadowEffect Color="Gray" Direction="315" ShadowDepth="4" Opacity="0.2" BlurRadius="10"/>
            </Border.Effect>
            <ListView ItemsSource="{Binding FilteredApartments}" BorderThickness="0" Background="Transparent">
                <ListView.View>
                    <GridView ColumnHeaderContainerStyle="{StaticResource ModernGridViewHeader}">
                        <GridViewColumn Header="Apartman Adı" DisplayMemberBinding="{Binding Name}" Width="200"/>
                        <GridViewColumn Header="Site" Width="200">
                            <GridViewColumn.CellTemplate>
                                <DataTemplate>
                                    <TextBlock Text="{Binding Site.Name}"/>
                                </DataTemplate>
                            </GridViewColumn.CellTemplate>
                        </GridViewColumn>
                        <GridViewColumn Header="Kat Sayısı" DisplayMemberBinding="{Binding FloorCount}" Width="100"/>
                        <GridViewColumn Header="Daire Sayısı" DisplayMemberBinding="{Binding UnitCount}" Width="100"/>
                        <GridViewColumn Header="Yapım Yılı" DisplayMemberBinding="{Binding BuildYear}" Width="100"/>
                        <GridViewColumn Header="Açıklama" Width="200">
                            <GridViewColumn.CellTemplate>
                                <DataTemplate>
                                    <TextBlock Text="{Binding Description}" TextWrapping="Wrap"/>
                                </DataTemplate>
                            </GridViewColumn.CellTemplate>
                        </GridViewColumn>
                        <GridViewColumn Header="İşlemler" Width="140">
                            <GridViewColumn.CellTemplate>
                                <DataTemplate>
                                    <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                                        <Button Content="Düzenle" 
                                               Style="{StaticResource SoftButton}" 
                                               Command="{Binding DataContext.EditApartmentCommand, RelativeSource={RelativeSource AncestorType=ListView}}" 
                                               CommandParameter="{Binding}" 
                                               Margin="0,0,8,0" 
                                               Width="60" 
                                               Height="28"/>
                                        <Button Content="Sil" 
                                               Style="{StaticResource DangerButton}" 
                                               Command="{Binding DataContext.DeleteApartmentCommand, RelativeSource={RelativeSource AncestorType=ListView}}" 
                                               CommandParameter="{Binding}" 
                                               Width="60" 
                                               Height="28"/>
                                    </StackPanel>
                                </DataTemplate>
                            </GridViewColumn.CellTemplate>
                        </GridViewColumn>
                    </GridView>
                </ListView.View>
            </ListView>
        </Border>
    </Grid>
</UserControl>
