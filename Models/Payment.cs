using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace ApartmanYonetimSistemi.Models
{
    public class Payment : BaseEntity
    {
        [StringLength(50)]
        public string TenantId { get; set; } = string.Empty;

        [Required]
        [StringLength(50)]
        public string UnitId { get; set; } = string.Empty;

        [StringLength(50)]
        public string ApartmentId { get; set; } = string.Empty;

        [StringLength(50)]
        public string SiteId { get; set; } = string.Empty;

        [Required]
        public PaymentType Type { get; set; } = PaymentType.Rent;

        [Required]
        [Column(TypeName = "decimal(18,2)")]
        public decimal Amount { get; set; } = 0;

        [Required]
        public DateTime DueDate { get; set; }

        public DateTime? PaidDate { get; set; }

        public PaymentMethod PaymentMethod { get; set; } = PaymentMethod.Cash;

        [Required]
        public PaymentStatus Status { get; set; } = PaymentStatus.Pending;

        [StringLength(500)]
        public string Description { get; set; } = string.Empty;

        [StringLength(50)]
        public string InvoiceNumber { get; set; } = string.Empty;

        [StringLength(50)]
        public string ReceiptNumber { get; set; } = string.Empty;

        [Column(TypeName = "decimal(18,2)")]
        public decimal LateFee { get; set; } = 0;

        [Column(TypeName = "decimal(18,2)")]
        public decimal Discount { get; set; } = 0;

        [Column(TypeName = "decimal(18,2)")]
        public decimal TotalAmount { get; set; } = 0;

        [StringLength(1000)]
        public string Notes { get; set; } = string.Empty;

        [StringLength(20)]
        public string Period { get; set; } = string.Empty; // "2024-01" gibi

        [StringLength(50)]
        public string CompanyId { get; set; } = string.Empty;

        // Navigation properties
        public virtual Unit Unit { get; set; } = null!;
        public virtual Tenant? Tenant { get; set; }

        // Computed properties
        [NotMapped]
        public bool IsOverdue => Status == PaymentStatus.Pending && DueDate < DateTime.UtcNow;

        [NotMapped]
        public int DaysOverdue => IsOverdue ? (DateTime.UtcNow - DueDate).Days : 0;

        [NotMapped]
        public decimal FinalAmount => Amount + LateFee - Discount;
    }
}
