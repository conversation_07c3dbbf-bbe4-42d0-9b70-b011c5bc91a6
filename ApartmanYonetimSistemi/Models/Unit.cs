using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace ApartmanYonetimSistemi.Models
{
    public class Unit : BaseEntity
    {
        [Required]
        [StringLength(50)]
        public string UnitNumber { get; set; } = string.Empty;

        public int Floor { get; set; } = 0;

        public UnitType Type { get; set; } = UnitType.Apartment;

        public UnitStatus Status { get; set; } = UnitStatus.Available;

        public int RoomCount { get; set; } = 0;

        public int BathroomCount { get; set; } = 0;

        public decimal Area { get; set; } = 0; // m²

        [Column(TypeName = "decimal(18,2)")]
        public decimal? RentPrice { get; set; }

        [Column(TypeName = "decimal(18,2)")]
        public decimal? SalePrice { get; set; }

        [StringLength(500)]
        public string? Description { get; set; }

        // Foreign Keys
        [Required]
        [StringLength(50)]
        public string ApartmentId { get; set; } = string.Empty;

        [StringLength(50)]
        public string? TenantId { get; set; } // Mevcut kiracı

        [StringLength(50)]
        public string UserId { get; set; } = string.Empty; // Daireyi oluşturan kullanıcı

        // Navigation Properties
        [ForeignKey("ApartmentId")]
        public virtual Apartment Apartment { get; set; } = null!;

        [ForeignKey("TenantId")]
        public virtual Tenant? Tenant { get; set; }

        [ForeignKey("UserId")]
        public virtual User User { get; set; } = null!;

        public virtual ICollection<Payment> Payments { get; set; } = new List<Payment>();
    }
}
