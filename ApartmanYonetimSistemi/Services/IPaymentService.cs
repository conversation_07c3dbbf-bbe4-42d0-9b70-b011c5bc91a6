using ApartmanYonetimSistemi.Models;

namespace ApartmanYonetimSistemi.Services
{
    public interface IPaymentService
    {
        Task<IEnumerable<Payment>> GetAllAsync();
        Task<Payment?> GetByIdAsync(string id);
        Task<IEnumerable<Payment>> GetByUserIdAsync(string userId);
        Task<IEnumerable<Payment>> GetByCompanyIdAsync(string? companyId);
        Task<IEnumerable<Payment>> GetByUnitIdAsync(string unitId);
        Task<IEnumerable<Payment>> GetByTenantIdAsync(string tenantId);
        Task<IEnumerable<Payment>> GetOverduePaymentsAsync();
        Task<Payment> CreateAsync(Payment payment);
        Task<Payment> UpdateAsync(Payment payment);
        Task DeleteAsync(string id);
        Task<decimal> GetTotalRevenueAsync(string? companyId = null);
        Task<decimal> GetPendingAmountAsync(string? companyId = null);
    }
}
