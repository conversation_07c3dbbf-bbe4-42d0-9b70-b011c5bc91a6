using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using ApartmanYonetimSistemi.Services;
using ApartmanYonetimSistemi.Models;
using System.Windows.Controls;
using System.Windows;

namespace ApartmanYonetimSistemi.ViewModels
{
    public partial class MainViewModel : BaseViewModel
    {
        private readonly IApartmanService _apartmanService;

        [ObservableProperty]
        private string _currentUserEmail = string.Empty;

        [ObservableProperty]
        private User? _currentUser;

        [ObservableProperty]
        private UserControl? _currentView;

        public MainViewModel(IApartmanService apartmanService)
        {
            _apartmanService = apartmanService;
            Title = "Apartman Yönetim Sistemi";
            
            // Başlangıçta dashboard'u göster
            ShowDashboard();
        }

        [RelayCommand]
        private void ShowDashboard()
        {
            // Dashboard view'ını yükle - şimdilik basit bir UserControl
            CurrentView = CreateSimpleView("Dashboard", "Hoş geldiniz! Dashboard burada olacak.");
        }

        [RelayCommand]
        private void ShowSites()
        {
            CurrentView = CreateSimpleView("Siteler", "Site yönetimi burada olacak.");
        }

        [RelayCommand]
        private void ShowApartments()
        {
            CurrentView = CreateSimpleView("Apartmanlar", "Apartman yönetimi burada olacak.");
        }

        [RelayCommand]
        private void ShowUnits()
        {
            CurrentView = CreateSimpleView("Daireler", "Daire yönetimi burada olacak.");
        }

        [RelayCommand]
        private void ShowTenants()
        {
            CurrentView = CreateSimpleView("Kiracılar", "Kiracı yönetimi burada olacak.");
        }

        [RelayCommand]
        private void ShowPayments()
        {
            CurrentView = CreateSimpleView("Ödemeler", "Ödeme yönetimi burada olacak.");
        }

        [RelayCommand]
        private void Logout()
        {
            // Logout işlemi
            Application.Current.Shutdown();
        }

        public void SetCurrentUser(User user)
        {
            CurrentUser = user;
            CurrentUserEmail = user.Email;
            Title = $"Apartman Yönetim Sistemi - {user.FullName}";
        }

        private UserControl CreateSimpleView(string title, string content)
        {
            var userControl = new UserControl();
            var stackPanel = new StackPanel
            {
                Margin = new Thickness(20)
            };

            var titleBlock = new TextBlock
            {
                Text = title,
                FontSize = 24,
                FontWeight = FontWeights.Bold,
                Margin = new Thickness(0, 0, 0, 20)
            };

            var contentBlock = new TextBlock
            {
                Text = content,
                FontSize = 16
            };

            stackPanel.Children.Add(titleBlock);
            stackPanel.Children.Add(contentBlock);
            userControl.Content = stackPanel;

            return userControl;
        }
    }
}
