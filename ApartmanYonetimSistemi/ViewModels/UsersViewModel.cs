using System.Collections.ObjectModel;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using ApartmanYonetimSistemi.Models;
using ApartmanYonetimSistemi.Services;
using System.Threading.Tasks;
using System.Windows;
using System.Linq;

namespace ApartmanYonetimSistemi.ViewModels
{
    public partial class UsersViewModel : BaseViewModel
    {
        private readonly IUserService _userService;
        private readonly ICompanyService _companyService;

        [ObservableProperty]
        private ObservableCollection<User> _users = new();

        [ObservableProperty]
        private ObservableCollection<User> _filteredUsers = new();

        [ObservableProperty]
        private ObservableCollection<Company> _companies = new();

        [ObservableProperty]
        private string _searchText = string.Empty;

        [ObservableProperty]
        private UserRole? _selectedRole;

        [ObservableProperty]
        private string? _selectedCompanyId;

        [ObservableProperty]
        private User? _selectedUser;

        private User? _currentUser;

        partial void OnSearchTextChanged(string value) => ApplyFilter();
        partial void OnSelectedRoleChanged(UserRole? value) => ApplyFilter();
        partial void OnSelectedCompanyIdChanged(string? value) => ApplyFilter();

        public UsersViewModel(IUserService userService, ICompanyService companyService)
        {
            _userService = userService;
            _companyService = companyService;
            Title = "Kullanıcı Yönetimi";
        }

        public void SetCurrentUser(User user)
        {
            _currentUser = user;
            _ = LoadDataAsync();
        }

        public async Task LoadDataAsync()
        {
            try
            {
                IsBusy = true;
                Users.Clear();
                Companies.Clear();

                if (_currentUser == null) return;

                // Sadece SuperAdmin ve Admin kullanıcı yönetimi yapabilir
                if (_currentUser.Role != UserRole.SuperAdmin && _currentUser.Role != UserRole.Admin)
                {
                    MessageBox.Show("Bu sayfaya erişim yetkiniz bulunmamaktadır.", "Yetkisiz Erişim", MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                IEnumerable<User> userList;
                IEnumerable<Company> companyList;

                if (_currentUser.Role == UserRole.SuperAdmin)
                {
                    userList = await _userService.GetAllAsync();
                    companyList = await _companyService.GetAllAsync();
                }
                else // Admin
                {
                    userList = await _userService.GetByCompanyIdAsync(_currentUser.CompanyId);
                    companyList = _currentUser.CompanyId != null 
                        ? new[] { await _companyService.GetByIdAsync(_currentUser.CompanyId) }.Where(c => c != null).Cast<Company>()
                        : new List<Company>();
                }

                // Şirket listesini doldur
                foreach (var company in companyList.OrderBy(c => c.Name))
                {
                    Companies.Add(company);
                }

                // Kullanıcı listesini doldur
                foreach (var user in userList.OrderBy(u => u.FirstName).ThenBy(u => u.LastName))
                {
                    Users.Add(user);
                }

                ApplyFilter();
            }
            catch (Exception ex)
            {
                Users.Clear();
                FilteredUsers.Clear();
                Companies.Clear();
                MessageBox.Show($"Veriler yüklenirken hata oluştu: {ex.Message}", "Hata", MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                IsBusy = false;
            }
        }

        private void ApplyFilter()
        {
            var query = Users.AsEnumerable();

            if (!string.IsNullOrWhiteSpace(SearchText))
            {
                var searchLower = SearchText.ToLower();
                query = query.Where(u => 
                    u.FirstName.ToLower().Contains(searchLower) ||
                    u.LastName.ToLower().Contains(searchLower) ||
                    u.Email.ToLower().Contains(searchLower));
            }

            if (SelectedRole.HasValue)
                query = query.Where(u => u.Role == SelectedRole.Value);

            if (!string.IsNullOrWhiteSpace(SelectedCompanyId))
                query = query.Where(u => u.CompanyId == SelectedCompanyId);

            var filtered = new ObservableCollection<User>();
            foreach (var user in query.OrderBy(u => u.FirstName).ThenBy(u => u.LastName))
            {
                filtered.Add(user);
            }

            FilteredUsers = filtered;
        }

        [RelayCommand]
        private async Task AddUser()
        {
            MessageBox.Show("Kullanıcı ekleme dialog'u yakında eklenecek!", "Bilgi", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        [RelayCommand]
        private async Task EditUser(User? user)
        {
            if (user == null) return;
            MessageBox.Show($"{user.FullName} kullanıcısı düzenleme dialog'u yakında eklenecek!", "Bilgi", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        [RelayCommand]
        private async Task ViewUserDetails(User? user)
        {
            if (user == null) return;
            MessageBox.Show($"{user.FullName} kullanıcısı detay sayfası yakında eklenecek!", "Bilgi", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        [RelayCommand]
        private async Task ToggleUserStatus(User? user)
        {
            if (user == null || _currentUser == null) return;

            // Kendi durumunu değiştiremez
            if (user.Id == _currentUser.Id)
            {
                MessageBox.Show("Kendi durumunuzu değiştiremezsiniz.", "Uyarı", MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            var action = user.IsActive ? "pasif" : "aktif";
            var result = MessageBox.Show($"{user.FullName} kullanıcısını {action} yapmak istediğinize emin misiniz?",
                                       "Kullanıcı Durumu Değiştir",
                                       MessageBoxButton.YesNo,
                                       MessageBoxImage.Question);

            if (result == MessageBoxResult.Yes)
            {
                try
                {
                    IsBusy = true;
                    user.IsActive = !user.IsActive;
                    await _userService.UpdateAsync(user);
                    
                    MessageBox.Show($"Kullanıcı başarıyla {action} yapıldı.", "Başarılı", MessageBoxButton.OK, MessageBoxImage.Information);
                }
                catch (Exception ex)
                {
                    user.IsActive = !user.IsActive; // Revert change
                    MessageBox.Show($"Kullanıcı durumu değiştirilirken hata oluştu: {ex.Message}", "Hata", MessageBoxButton.OK, MessageBoxImage.Error);
                }
                finally
                {
                    IsBusy = false;
                }
            }
        }

        [RelayCommand]
        private void ClearFilters()
        {
            SearchText = string.Empty;
            SelectedRole = null;
            SelectedCompanyId = null;
            ApplyFilter();
        }

        [RelayCommand]
        private async Task RefreshData()
        {
            await LoadDataAsync();
        }
    }
}
