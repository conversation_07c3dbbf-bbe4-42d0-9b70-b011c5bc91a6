using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace ApartmanYonetimSistemi.Models
{
    public class User : BaseEntity
    {
        [Required]
        [StringLength(255)]
        [EmailAddress]
        public string Email { get; set; } = string.Empty;

        [StringLength(100)]
        public string FirstName { get; set; } = string.Empty;

        [StringLength(100)]
        public string LastName { get; set; } = string.Empty;

        [StringLength(20)]
        public string PhoneNumber { get; set; } = string.Empty;

        [Required]
        public UserRole Role { get; set; } = UserRole.Tenant;

        [Required]
        public UserStatus Status { get; set; } = UserStatus.Active;

        [StringLength(50)]
        public string CompanyId { get; set; } = string.Empty;

        [StringLength(255)]
        public string PasswordHash { get; set; } = string.Empty;

        public DateTime? LastLoginAt { get; set; }

        public bool IsEmailVerified { get; set; } = false;

        [NotMapped]
        public string FullName => $"{FirstName} {LastName}".Trim();
    }
}
