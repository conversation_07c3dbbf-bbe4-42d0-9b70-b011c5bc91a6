using ApartmanYonetimSistemi.Models;

namespace ApartmanYonetimSistemi.Services
{
    public interface ITenantService
    {
        Task<IEnumerable<Tenant>> GetAllAsync();
        Task<Tenant?> GetByIdAsync(string id);
        Task<IEnumerable<Tenant>> GetByUserIdAsync(string userId);
        Task<IEnumerable<Tenant>> GetByCompanyIdAsync(string? companyId);
        Task<Tenant> CreateAsync(Tenant tenant);
        Task<Tenant> UpdateAsync(Tenant tenant);
        Task DeleteAsync(string id);
    }
}
