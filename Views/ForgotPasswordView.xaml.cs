using System.Windows;
using ApartmanYonetimSistemi.ViewModels;
using Microsoft.Extensions.DependencyInjection;

namespace ApartmanYonetimSistemi.Views
{
    /// <summary>
    /// ForgotPasswordView.xaml etkileşim mantığı
    /// </summary>
    public partial class ForgotPasswordView : Window
    {
        public ForgotPasswordView()
        {
            InitializeComponent();

            // ViewModel'i DI container'dan al
            var app = (App)Application.Current;
            var viewModel = app.Services.GetRequiredService<ForgotPasswordViewModel>();
            viewModel.RequestClose += () => Close();
            DataContext = viewModel;
        }

        private void NewPasswordBox_PasswordChanged(object sender, RoutedEventArgs e)
        {
            if (DataContext is ForgotPasswordViewModel viewModel && sender is System.Windows.Controls.PasswordBox passwordBox)
            {
                viewModel.NewPassword = passwordBox.Password;
            }
        }

        private void ConfirmPasswordBox_PasswordChanged(object sender, RoutedEventArgs e)
        {
            if (DataContext is ForgotPasswordViewModel viewModel && sender is System.Windows.Controls.PasswordBox passwordBox)
            {
                viewModel.ConfirmPassword = passwordBox.Password;
            }
        }
    }
}
