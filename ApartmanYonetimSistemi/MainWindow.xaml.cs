using System.Windows;
using System.Windows.Controls;
using ApartmanYonetimSistemi.ViewModels;
using ApartmanYonetimSistemi.Views;
using Microsoft.Extensions.DependencyInjection;

namespace ApartmanYonetimSistemi;

/// <summary>
/// Interaction logic for MainWindow.xaml
/// </summary>
public partial class MainWindow : Window
{
    public MainWindow(MainViewModel viewModel)
    {
        InitializeComponent();
        DataContext = viewModel;

        // ViewModel değişikliklerini dinle
        viewModel.PropertyChanged += ViewModel_PropertyChanged;
    }

    private void ViewModel_PropertyChanged(object? sender, System.ComponentModel.PropertyChangedEventArgs e)
    {
        if (sender is MainViewModel viewModel)
        {
            if (e.PropertyName == nameof(MainViewModel.CurrentUserName))
            {
                UserInfoText.Text = $"Kullanıcı: {viewModel.CurrentUserName}";
            }
            else if (e.PropertyName == nameof(MainViewModel.IsAdminOrSuperAdmin))
            {
                UserManagementButton.Visibility = viewModel.IsAdminOrSuperAdmin ?
                    System.Windows.Visibility.Visible : System.Windows.Visibility.Collapsed;
            }
        }
    }

    private void ExitButton_Click(object sender, RoutedEventArgs e)
    {
        Application.Current.Shutdown();
    }

    private void DashboardButton_Click(object sender, RoutedEventArgs e)
    {
        ShowDashboard();
    }

    private void SitesButton_Click(object sender, RoutedEventArgs e)
    {
        var app = (App)Application.Current;
        var sitesView = app.Services.GetRequiredService<SitesView>();
        ContentArea.Content = sitesView;
    }

    private void ApartmentsButton_Click(object sender, RoutedEventArgs e)
    {
        var app = (App)Application.Current;
        var apartmentsView = app.Services.GetRequiredService<ApartmentsView>();
        ContentArea.Content = apartmentsView;
    }

    private void UnitsButton_Click(object sender, RoutedEventArgs e)
    {
        var app = (App)Application.Current;
        var unitsView = app.Services.GetRequiredService<UnitsView>();
        ContentArea.Content = unitsView;
    }

    private void TenantsButton_Click(object sender, RoutedEventArgs e)
    {
        var app = (App)Application.Current;
        var tenantsView = app.Services.GetRequiredService<TenantsView>();
        ContentArea.Content = tenantsView;
    }

    private void PaymentsButton_Click(object sender, RoutedEventArgs e)
    {
        var app = (App)Application.Current;
        var paymentsView = app.Services.GetRequiredService<PaymentsView>();
        ContentArea.Content = paymentsView;
    }

    private void UsersButton_Click(object sender, RoutedEventArgs e)
    {
        var app = (App)Application.Current;
        var usersView = app.Services.GetRequiredService<UsersView>();
        ContentArea.Content = usersView;
    }

    private void UserManagementButton_Click(object sender, RoutedEventArgs e)
    {
        if (DataContext is MainViewModel viewModel)
        {
            viewModel.ShowUserManagementCommand.Execute(null);
        }
    }

    private void ShowDashboard()
    {
        var app = (App)Application.Current;
        var dashboardView = app.Services.GetRequiredService<DashboardView>();
        ContentArea.Content = dashboardView;
    }

    private void ShowContent(string title, string content)
    {
        var stackPanel = new StackPanel { Margin = new Thickness(20) };

        var titleBlock = new TextBlock
        {
            Text = title,
            FontSize = 24,
            FontWeight = FontWeights.Bold,
            Margin = new Thickness(0, 0, 0, 20)
        };

        var contentBlock = new TextBlock
        {
            Text = content,
            FontSize = 16
        };

        stackPanel.Children.Add(titleBlock);
        stackPanel.Children.Add(contentBlock);

        ContentArea.Content = stackPanel;
    }
}