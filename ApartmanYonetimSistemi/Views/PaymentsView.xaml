<UserControl x:Class="ApartmanYonetimSistemi.Views.PaymentsView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:converters="clr-namespace:ApartmanYonetimSistemi.Converters">

    <UserControl.Resources>
        <!-- Payment Status Color Converter -->
        <Style x:Key="PaymentStatusIndicator" TargetType="Ellipse">
            <Setter Property="Width" Value="12"/>
            <Setter Property="Height" Value="12"/>
            <Setter Property="VerticalAlignment" Value="Center"/>
            <Setter Property="HorizontalAlignment" Value="Center"/>
            <Style.Triggers>
                <DataTrigger Binding="{Binding Status}" Value="Pending">
                    <Setter Property="Fill" Value="#FF9800"/>
                </DataTrigger>
                <DataTrigger Binding="{Binding Status}" Value="Paid">
                    <Setter Property="Fill" Value="#4CAF50"/>
                </DataTrigger>
                <DataTrigger Binding="{Binding Status}" Value="Overdue">
                    <Setter Property="Fill" Value="#F44336"/>
                </DataTrigger>
                <DataTrigger Binding="{Binding Status}" Value="Cancelled">
                    <Setter Property="Fill" Value="#9E9E9E"/>
                </DataTrigger>
                <DataTrigger Binding="{Binding Status}" Value="Refunded">
                    <Setter Property="Fill" Value="#2196F3"/>
                </DataTrigger>
            </Style.Triggers>
        </Style>
    </UserControl.Resources>

    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <Border Grid.Row="0" Style="{StaticResource CardStyle}" Margin="0,0,0,10">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <StackPanel Grid.Column="0">
                    <TextBlock Text="💰 Ödeme Yönetimi" Style="{StaticResource TitleStyle}"/>
                    <TextBlock Text="Ödemeleri görüntüleyin, ekleyin ve yönetin" 
                               Style="{StaticResource SubtitleStyle}" Opacity="0.7"/>
                </StackPanel>

                <StackPanel Grid.Column="1" Orientation="Horizontal">
                    <Button Content="➕ Yeni Ödeme" 
                            Style="{StaticResource PrimaryButton}"
                            Command="{Binding AddPaymentCommand}"
                            Margin="0,0,10,0"/>
                    <Button Content="🔄 Yenile" 
                            Style="{StaticResource SecondaryButton}"
                            Command="{Binding RefreshDataCommand}"/>
                </StackPanel>
            </Grid>
        </Border>

        <!-- Filters -->
        <Border Grid.Row="1" Style="{StaticResource CardStyle}" Margin="0,0,0,10">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>

                <TextBlock Grid.Row="0" Grid.ColumnSpan="4" Text="Filtreler" 
                           Style="{StaticResource SubtitleStyle}" Margin="0,0,0,10"/>

                <!-- Payment Type Filter -->
                <StackPanel Grid.Row="1" Grid.Column="0" Margin="0,0,10,0">
                    <TextBlock Text="Ödeme Türü" FontWeight="SemiBold" FontSize="12" Foreground="#666" Margin="0,0,0,2"/>
                    <ComboBox SelectedValue="{Binding SelectedPaymentType}" Height="32">
                        <ComboBoxItem Content="Tümü" Tag="{x:Null}"/>
                        <ComboBoxItem Content="Kira" Tag="Rent"/>
                        <ComboBoxItem Content="Aidat" Tag="Maintenance"/>
                        <ComboBoxItem Content="Faturalar" Tag="Utilities"/>
                        <ComboBoxItem Content="Depozito" Tag="Deposit"/>
                        <ComboBoxItem Content="Gecikme Cezası" Tag="LateFee"/>
                        <ComboBoxItem Content="Diğer" Tag="Other"/>
                    </ComboBox>
                </StackPanel>

                <!-- Status Filter -->
                <StackPanel Grid.Row="1" Grid.Column="1" Margin="0,0,10,0">
                    <TextBlock Text="Durum" FontWeight="SemiBold" FontSize="12" Foreground="#666" Margin="0,0,0,2"/>
                    <ComboBox SelectedValue="{Binding SelectedStatus}" Height="32">
                        <ComboBoxItem Content="Tümü" Tag="{x:Null}"/>
                        <ComboBoxItem Content="Beklemede" Tag="Pending"/>
                        <ComboBoxItem Content="Ödendi" Tag="Paid"/>
                        <ComboBoxItem Content="Gecikmiş" Tag="Overdue"/>
                        <ComboBoxItem Content="İptal" Tag="Cancelled"/>
                        <ComboBoxItem Content="İade" Tag="Refunded"/>
                    </ComboBox>
                </StackPanel>

                <!-- Date Range -->
                <StackPanel Grid.Row="1" Grid.Column="2" Margin="0,0,10,0">
                    <TextBlock Text="Tarih Aralığı" FontWeight="SemiBold" FontSize="12" Foreground="#666" Margin="0,0,0,2"/>
                    <ComboBox SelectedValue="{Binding SelectedDateRange}" Height="32">
                        <ComboBoxItem Content="Tümü" Tag="All"/>
                        <ComboBoxItem Content="Bu Ay" Tag="ThisMonth"/>
                        <ComboBoxItem Content="Son 3 Ay" Tag="Last3Months"/>
                        <ComboBoxItem Content="Bu Yıl" Tag="ThisYear"/>
                    </ComboBox>
                </StackPanel>

                <!-- Clear Filters -->
                <StackPanel Grid.Row="1" Grid.Column="3" VerticalAlignment="Bottom">
                    <Button Content="Temizle" 
                            Style="{StaticResource SecondaryButton}"
                            Command="{Binding ClearFiltersCommand}"
                            Padding="15,8"/>
                </StackPanel>
            </Grid>
        </Border>

        <!-- Payments List -->
        <Border Grid.Row="2" Style="{StaticResource CardStyle}">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                </Grid.RowDefinitions>

                <!-- List Header -->
                <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="0,0,0,10">
                    <TextBlock Text="Ödemeler" Style="{StaticResource SubtitleStyle}"/>
                    <TextBlock Text="{Binding TotalAmount, StringFormat='Toplam: {0:C}'}" 
                               FontWeight="Bold" Foreground="#2196F3" 
                               VerticalAlignment="Center" Margin="20,0,0,0"/>
                </StackPanel>

                <!-- Loading Indicator -->
                <ProgressBar Grid.Row="1" IsIndeterminate="True" 
                             Visibility="{Binding IsBusy, Converter={x:Static converters:BooleanToVisibilityConverter.Instance}}"
                             Height="4" VerticalAlignment="Top"/>

                <!-- Data Grid -->
                <DataGrid Grid.Row="1" 
                          ItemsSource="{Binding FilteredPayments}"
                          SelectedItem="{Binding SelectedPayment}"
                          AutoGenerateColumns="False"
                          CanUserAddRows="False"
                          CanUserDeleteRows="False"
                          IsReadOnly="True"
                          GridLinesVisibility="Horizontal"
                          HeadersVisibility="Column"
                          SelectionMode="Single"
                          AlternatingRowBackground="#F9F9F9">

                    <DataGrid.Columns>
                        <!-- Status -->
                        <DataGridTemplateColumn Header="Durum" Width="60">
                            <DataGridTemplateColumn.CellTemplate>
                                <DataTemplate>
                                    <Ellipse Style="{StaticResource PaymentStatusIndicator}"/>
                                </DataTemplate>
                            </DataGridTemplateColumn.CellTemplate>
                        </DataGridTemplateColumn>

                        <!-- Description -->
                        <DataGridTextColumn Header="Açıklama" Binding="{Binding Description}" Width="200"/>

                        <!-- Unit -->
                        <DataGridTextColumn Header="Daire" Width="100">
                            <DataGridTextColumn.Binding>
                                <Binding Path="Unit.UnitNumber"/>
                            </DataGridTextColumn.Binding>
                        </DataGridTextColumn>

                        <!-- Tenant -->
                        <DataGridTextColumn Header="Kiracı" Width="150">
                            <DataGridTextColumn.Binding>
                                <Binding Path="Tenant.FullName"/>
                            </DataGridTextColumn.Binding>
                        </DataGridTextColumn>

                        <!-- Type -->
                        <DataGridTextColumn Header="Tür" Width="100">
                            <DataGridTextColumn.Binding>
                                <Binding Path="Type" Converter="{x:Static converters:PaymentTypeConverter.Instance}"/>
                            </DataGridTextColumn.Binding>
                        </DataGridTextColumn>

                        <!-- Amount -->
                        <DataGridTextColumn Header="Tutar" Width="100">
                            <DataGridTextColumn.Binding>
                                <Binding Path="Amount" StringFormat="C"/>
                            </DataGridTextColumn.Binding>
                        </DataGridTextColumn>

                        <!-- Due Date -->
                        <DataGridTextColumn Header="Vade Tarihi" Width="100">
                            <DataGridTextColumn.Binding>
                                <Binding Path="DueDate" StringFormat="dd.MM.yyyy"/>
                            </DataGridTextColumn.Binding>
                        </DataGridTextColumn>

                        <!-- Paid Date -->
                        <DataGridTextColumn Header="Ödeme Tarihi" Width="100">
                            <DataGridTextColumn.Binding>
                                <Binding Path="PaidDate" StringFormat="dd.MM.yyyy"/>
                            </DataGridTextColumn.Binding>
                        </DataGridTextColumn>

                        <!-- Status Text -->
                        <DataGridTextColumn Header="Durum" Width="100">
                            <DataGridTextColumn.Binding>
                                <Binding Path="Status" Converter="{x:Static converters:PaymentStatusConverter.Instance}"/>
                            </DataGridTextColumn.Binding>
                        </DataGridTextColumn>

                        <!-- Actions -->
                        <DataGridTemplateColumn Header="İşlemler" Width="200">
                            <DataGridTemplateColumn.CellTemplate>
                                <DataTemplate>
                                    <StackPanel Orientation="Horizontal">
                                        <Button Content="👁️" 
                                                Command="{Binding DataContext.ViewPaymentDetailsCommand, RelativeSource={RelativeSource AncestorType=UserControl}}"
                                                CommandParameter="{Binding}"
                                                Style="{StaticResource SecondaryButton}"
                                                Padding="8,4" Margin="0,0,5,0"
                                                ToolTip="Detayları Görüntüle"/>
                                        <Button Content="✏️" 
                                                Command="{Binding DataContext.EditPaymentCommand, RelativeSource={RelativeSource AncestorType=UserControl}}"
                                                CommandParameter="{Binding}"
                                                Style="{StaticResource SecondaryButton}"
                                                Padding="8,4" Margin="0,0,5,0"
                                                ToolTip="Düzenle"/>
                                        <Button Content="🗑️" 
                                                Command="{Binding DataContext.DeletePaymentCommand, RelativeSource={RelativeSource AncestorType=UserControl}}"
                                                CommandParameter="{Binding}"
                                                Style="{StaticResource SecondaryButton}"
                                                Padding="8,4"
                                                ToolTip="Sil"/>
                                    </StackPanel>
                                </DataTemplate>
                            </DataGridTemplateColumn.CellTemplate>
                        </DataGridTemplateColumn>
                    </DataGrid.Columns>
                </DataGrid>

                <!-- Empty State -->
                <StackPanel Grid.Row="1"
                            Visibility="{Binding FilteredPayments.Count, Converter={x:Static converters:EmptyStateConverter.Instance}}"
                            VerticalAlignment="Center" HorizontalAlignment="Center">
                    <TextBlock Text="💰" FontSize="48" HorizontalAlignment="Center" Opacity="0.3"/>
                    <TextBlock Text="Henüz ödeme kaydı eklenmemiş" 
                               FontSize="16" HorizontalAlignment="Center" 
                               Margin="0,10,0,5" Opacity="0.6"/>
                    <TextBlock Text="İlk ödeme kaydını eklemek için 'Yeni Ödeme' butonuna tıklayın" 
                               FontSize="12" HorizontalAlignment="Center" 
                               Opacity="0.5"/>
                </StackPanel>
            </Grid>
        </Border>
    </Grid>
</UserControl>
