<Window x:Class="ApartmanYonetimSistemi.Views.Dialogs.PaymentDialog"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:converters="clr-namespace:ApartmanYonetimSistemi.Converters"
        Title="{Binding Title}" 
        Height="600" Width="500"
        WindowStartupLocation="CenterOwner"
        ResizeMode="NoResize"
        Background="#F5F5F5">

    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <Border Grid.Row="0" Style="{StaticResource CardStyle}" Margin="0,0,0,20">
            <StackPanel>
                <TextBlock Text="{Binding Title}" Style="{StaticResource TitleStyle}"/>
                <TextBlock Text="Ödeme bilgilerini girin" 
                           Style="{StaticResource SubtitleStyle}" Opacity="0.7"/>
            </StackPanel>
        </Border>

        <!-- Form -->
        <Border Grid.Row="1" Style="{StaticResource CardStyle}" Margin="0,0,0,20">
            <ScrollViewer VerticalScrollBarVisibility="Auto">
                <StackPanel>
                    <!-- Description -->
                    <StackPanel Margin="0,0,0,15">
                        <TextBlock Text="Açıklama *" FontWeight="SemiBold" Margin="0,0,0,5"/>
                        <TextBox Text="{Binding Description, UpdateSourceTrigger=PropertyChanged}"
                                 Height="35" Padding="10,0"/>
                        <TextBlock Text="{Binding DescriptionError}" 
                                   Foreground="Red" FontSize="12" Margin="0,2,0,0"
                                   Visibility="{Binding DescriptionError, Converter={x:Static converters:StringToVisibilityConverter.Instance}}"/>
                    </StackPanel>

                    <!-- Unit and Tenant -->
                    <Grid Margin="0,0,0,15">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="10"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>

                        <!-- Unit -->
                        <StackPanel Grid.Column="0">
                            <TextBlock Text="Daire *" FontWeight="SemiBold" Margin="0,0,0,5"/>
                            <ComboBox ItemsSource="{Binding Units}"
                                      SelectedValue="{Binding SelectedUnitId}"
                                      SelectedValuePath="Id"
                                      Height="35">
                                <ComboBox.ItemTemplate>
                                    <DataTemplate>
                                        <TextBlock Text="{Binding UnitNumber}" />
                                    </DataTemplate>
                                </ComboBox.ItemTemplate>
                            </ComboBox>
                        </StackPanel>

                        <!-- Tenant -->
                        <StackPanel Grid.Column="2">
                            <TextBlock Text="Kiracı" FontWeight="SemiBold" Margin="0,0,0,5"/>
                            <ComboBox ItemsSource="{Binding Tenants}"
                                      SelectedValue="{Binding SelectedTenantId}"
                                      SelectedValuePath="Id"
                                      Height="35">
                                <ComboBox.ItemTemplate>
                                    <DataTemplate>
                                        <TextBlock Text="{Binding FullName}" />
                                    </DataTemplate>
                                </ComboBox.ItemTemplate>
                            </ComboBox>
                        </StackPanel>
                    </Grid>

                    <!-- Type and Status -->
                    <Grid Margin="0,0,0,15">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="10"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>

                        <!-- Type -->
                        <StackPanel Grid.Column="0">
                            <TextBlock Text="Ödeme Türü *" FontWeight="SemiBold" Margin="0,0,0,5"/>
                            <ComboBox SelectedValue="{Binding SelectedType}" Height="35">
                                <ComboBoxItem Content="Kira" Tag="Rent"/>
                                <ComboBoxItem Content="Aidat" Tag="Maintenance"/>
                                <ComboBoxItem Content="Faturalar" Tag="Utilities"/>
                                <ComboBoxItem Content="Depozito" Tag="Deposit"/>
                                <ComboBoxItem Content="Gecikme Cezası" Tag="LateFee"/>
                                <ComboBoxItem Content="Diğer" Tag="Other"/>
                            </ComboBox>
                        </StackPanel>

                        <!-- Status -->
                        <StackPanel Grid.Column="2">
                            <TextBlock Text="Durum *" FontWeight="SemiBold" Margin="0,0,0,5"/>
                            <ComboBox SelectedValue="{Binding SelectedStatus}" Height="35">
                                <ComboBoxItem Content="Beklemede" Tag="Pending"/>
                                <ComboBoxItem Content="Ödendi" Tag="Paid"/>
                                <ComboBoxItem Content="Gecikmiş" Tag="Overdue"/>
                                <ComboBoxItem Content="İptal" Tag="Cancelled"/>
                                <ComboBoxItem Content="İade" Tag="Refunded"/>
                            </ComboBox>
                        </StackPanel>
                    </Grid>

                    <!-- Amount -->
                    <StackPanel Margin="0,0,0,15">
                        <TextBlock Text="Tutar (₺) *" FontWeight="SemiBold" Margin="0,0,0,5"/>
                        <TextBox Text="{Binding Amount, UpdateSourceTrigger=PropertyChanged}"
                                 Height="35" Padding="10,0"/>
                        <TextBlock Text="{Binding AmountError}" 
                                   Foreground="Red" FontSize="12" Margin="0,2,0,0"
                                   Visibility="{Binding AmountError, Converter={x:Static converters:StringToVisibilityConverter.Instance}}"/>
                    </StackPanel>

                    <!-- Due Date and Paid Date -->
                    <Grid Margin="0,0,0,15">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="10"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>

                        <!-- Due Date -->
                        <StackPanel Grid.Column="0">
                            <TextBlock Text="Vade Tarihi *" FontWeight="SemiBold" Margin="0,0,0,5"/>
                            <DatePicker SelectedDate="{Binding DueDate}" Height="35"/>
                        </StackPanel>

                        <!-- Paid Date -->
                        <StackPanel Grid.Column="2">
                            <TextBlock Text="Ödeme Tarihi" FontWeight="SemiBold" Margin="0,0,0,5"/>
                            <DatePicker SelectedDate="{Binding PaidDate}" Height="35"/>
                        </StackPanel>
                    </Grid>

                    <!-- Notes -->
                    <StackPanel Margin="0,0,0,15">
                        <TextBlock Text="Notlar" FontWeight="SemiBold" Margin="0,0,0,5"/>
                        <TextBox Text="{Binding Notes, UpdateSourceTrigger=PropertyChanged}"
                                 Height="80" Padding="10,5"
                                 TextWrapping="Wrap"
                                 AcceptsReturn="True"
                                 VerticalScrollBarVisibility="Auto"/>
                    </StackPanel>
                </StackPanel>
            </ScrollViewer>
        </Border>

        <!-- Buttons -->
        <Border Grid.Row="2" Style="{StaticResource CardStyle}">
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Right">
                <Button Content="İptal" 
                        Style="{StaticResource SecondaryButton}"
                        Command="{Binding CancelCommand}"
                        Margin="0,0,10,0" 
                        Padding="20,10" 
                        MinWidth="80"/>
                <Button Content="{Binding SaveButtonText}" 
                        Style="{StaticResource PrimaryButton}"
                        Command="{Binding SaveCommand}"
                        Padding="20,10" 
                        MinWidth="80"/>
            </StackPanel>
        </Border>

        <!-- Loading Overlay -->
        <Border Grid.RowSpan="3" 
                Background="#80000000" 
                Visibility="{Binding IsBusy, Converter={x:Static converters:BooleanToVisibilityConverter.Instance}}">
            <StackPanel VerticalAlignment="Center" HorizontalAlignment="Center">
                <ProgressBar IsIndeterminate="True" Width="200" Height="4" Margin="0,0,0,10"/>
                <TextBlock Text="Kaydediliyor..." Foreground="White" FontSize="14" HorizontalAlignment="Center"/>
            </StackPanel>
        </Border>
    </Grid>
</Window>
