<UserControl x:Class="ApartmanYonetimSistemi.Views.UnitsView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:converters="clr-namespace:ApartmanYonetimSistemi.Converters">

    <UserControl.Resources>
        <!-- Status Color Converter -->
        <Style x:Key="StatusIndicator" TargetType="Ellipse">
            <Setter Property="Width" Value="12"/>
            <Setter Property="Height" Value="12"/>
            <Setter Property="VerticalAlignment" Value="Center"/>
            <Setter Property="HorizontalAlignment" Value="Center"/>
            <Style.Triggers>
                <DataTrigger Binding="{Binding Status}" Value="Available">
                    <Setter Property="Fill" Value="#4CAF50"/>
                </DataTrigger>
                <DataTrigger Binding="{Binding Status}" Value="Rented">
                    <Setter Property="Fill" Value="#2196F3"/>
                </DataTrigger>
                <DataTrigger Binding="{Binding Status}" Value="OwnerOccupied">
                    <Setter Property="Fill" Value="#FF9800"/>
                </DataTrigger>
                <DataTrigger Binding="{Binding Status}" Value="UnderMaintenance">
                    <Setter Property="Fill" Value="#F44336"/>
                </DataTrigger>
                <DataTrigger Binding="{Binding Status}" Value="ForSale">
                    <Setter Property="Fill" Value="#9C27B0"/>
                </DataTrigger>
            </Style.Triggers>
        </Style>
    </UserControl.Resources>

    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <Border Grid.Row="0" Style="{StaticResource CardStyle}" Margin="0,0,0,10">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <StackPanel Grid.Column="0">
                    <TextBlock Text="🚪 Daire Yönetimi" Style="{StaticResource TitleStyle}"/>
                    <TextBlock Text="Daireleri görüntüleyin, ekleyin ve yönetin" 
                               Style="{StaticResource SubtitleStyle}" Opacity="0.7"/>
                </StackPanel>

                <StackPanel Grid.Column="1" Orientation="Horizontal">
                    <Button Content="➕ Yeni Daire" 
                            Style="{StaticResource PrimaryButton}"
                            Command="{Binding AddUnitCommand}"
                            Margin="0,0,10,0"/>
                    <Button Content="🔄 Yenile" 
                            Style="{StaticResource SecondaryButton}"
                            Command="{Binding RefreshDataCommand}"/>
                </StackPanel>
            </Grid>
        </Border>

        <!-- Filters -->
        <Border Grid.Row="1" Style="{StaticResource CardStyle}" Margin="0,0,0,10">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="2*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>

                <TextBlock Grid.Row="0" Grid.ColumnSpan="4" Text="Filtreler" 
                           Style="{StaticResource SubtitleStyle}" Margin="0,0,0,10"/>

                <!-- Search -->
                <StackPanel Grid.Row="1" Grid.Column="0" Margin="0,0,10,0">
                    <TextBlock Text="Arama" FontWeight="SemiBold" FontSize="12" Foreground="#666" Margin="0,0,0,2"/>
                    <TextBox Text="{Binding SearchText, UpdateSourceTrigger=PropertyChanged}"
                             Height="32" Padding="8,0"
                             ToolTip="Daire numarası veya açıklama..."/>
                </StackPanel>

                <!-- Apartment Filter -->
                <StackPanel Grid.Row="1" Grid.Column="1" Margin="0,0,10,0">
                    <TextBlock Text="Apartman" FontWeight="SemiBold" FontSize="12" Foreground="#666" Margin="0,0,0,2"/>
                    <ComboBox SelectedValue="{Binding SelectedApartmentId}"
                              SelectedValuePath="Id"
                              ItemsSource="{Binding Apartments}"
                              DisplayMemberPath="Name"
                              Height="32"/>
                </StackPanel>

                <!-- Status Filter -->
                <StackPanel Grid.Row="1" Grid.Column="2" Margin="0,0,10,0">
                    <TextBlock Text="Durum" FontWeight="SemiBold" FontSize="12" Foreground="#666" Margin="0,0,0,2"/>
                    <ComboBox SelectedValue="{Binding SelectedStatus}" Height="32">
                        <ComboBoxItem Content="Tümü" Tag="{x:Null}"/>
                        <ComboBoxItem Content="Müsait" Tag="Available"/>
                        <ComboBoxItem Content="Kiralanmış" Tag="Rented"/>
                        <ComboBoxItem Content="Mal Sahibi" Tag="OwnerOccupied"/>
                        <ComboBoxItem Content="Bakımda" Tag="UnderMaintenance"/>
                        <ComboBoxItem Content="Satılık" Tag="ForSale"/>
                    </ComboBox>
                </StackPanel>

                <!-- Clear Filters -->
                <StackPanel Grid.Row="1" Grid.Column="3" VerticalAlignment="Bottom">
                    <Button Content="Temizle" 
                            Style="{StaticResource SecondaryButton}"
                            Command="{Binding ClearFiltersCommand}"
                            Padding="15,8"/>
                </StackPanel>
            </Grid>
        </Border>

        <!-- Units List -->
        <Border Grid.Row="2" Style="{StaticResource CardStyle}">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                </Grid.RowDefinitions>

                <!-- List Header -->
                <TextBlock Grid.Row="0" Text="Daireler" Style="{StaticResource SubtitleStyle}" Margin="0,0,0,10"/>

                <!-- Loading Indicator -->
                <ProgressBar Grid.Row="1" IsIndeterminate="True" 
                             Visibility="{Binding IsBusy, Converter={x:Static converters:BooleanToVisibilityConverter.Instance}}"
                             Height="4" VerticalAlignment="Top"/>

                <!-- Data Grid -->
                <DataGrid Grid.Row="1" 
                          ItemsSource="{Binding FilteredUnits}"
                          SelectedItem="{Binding SelectedUnit}"
                          AutoGenerateColumns="False"
                          CanUserAddRows="False"
                          CanUserDeleteRows="False"
                          IsReadOnly="True"
                          GridLinesVisibility="Horizontal"
                          HeadersVisibility="Column"
                          SelectionMode="Single"
                          AlternatingRowBackground="#F9F9F9">

                    <DataGrid.Columns>
                        <!-- Status -->
                        <DataGridTemplateColumn Header="Durum" Width="60">
                            <DataGridTemplateColumn.CellTemplate>
                                <DataTemplate>
                                    <Ellipse Style="{StaticResource StatusIndicator}"/>
                                </DataTemplate>
                            </DataGridTemplateColumn.CellTemplate>
                        </DataGridTemplateColumn>

                        <!-- Unit Number -->
                        <DataGridTextColumn Header="Daire No" Binding="{Binding UnitNumber}" Width="80"/>

                        <!-- Apartment -->
                        <DataGridTextColumn Header="Apartman" Width="150">
                            <DataGridTextColumn.Binding>
                                <Binding Path="Apartment.Name"/>
                            </DataGridTextColumn.Binding>
                        </DataGridTextColumn>

                        <!-- Floor -->
                        <DataGridTextColumn Header="Kat" Binding="{Binding Floor}" Width="50"/>

                        <!-- Type -->
                        <DataGridTextColumn Header="Tür" Width="80">
                            <DataGridTextColumn.Binding>
                                <Binding Path="Type" Converter="{x:Static converters:UnitTypeConverter.Instance}"/>
                            </DataGridTextColumn.Binding>
                        </DataGridTextColumn>

                        <!-- Room Count -->
                        <DataGridTextColumn Header="Oda" Binding="{Binding RoomCount}" Width="60"/>

                        <!-- Area -->
                        <DataGridTextColumn Header="Alan (m²)" Width="80">
                            <DataGridTextColumn.Binding>
                                <Binding Path="Area" StringFormat="N0"/>
                            </DataGridTextColumn.Binding>
                        </DataGridTextColumn>

                        <!-- Monthly Rent -->
                        <DataGridTextColumn Header="Kira (₺)" Width="100">
                            <DataGridTextColumn.Binding>
                                <Binding Path="RentPrice" StringFormat="C0"/>
                            </DataGridTextColumn.Binding>
                        </DataGridTextColumn>

                        <!-- Status Text -->
                        <DataGridTextColumn Header="Durum" Width="100">
                            <DataGridTextColumn.Binding>
                                <Binding Path="Status" Converter="{x:Static converters:UnitStatusConverter.Instance}"/>
                            </DataGridTextColumn.Binding>
                        </DataGridTextColumn>

                        <!-- Actions -->
                        <DataGridTemplateColumn Header="İşlemler" Width="200">
                            <DataGridTemplateColumn.CellTemplate>
                                <DataTemplate>
                                    <StackPanel Orientation="Horizontal">
                                        <Button Content="👁️" 
                                                Command="{Binding DataContext.ViewUnitDetailsCommand, RelativeSource={RelativeSource AncestorType=UserControl}}"
                                                CommandParameter="{Binding}"
                                                Style="{StaticResource SecondaryButton}"
                                                Padding="8,4" Margin="0,0,5,0"
                                                ToolTip="Detayları Görüntüle"/>
                                        <Button Content="✏️" 
                                                Command="{Binding DataContext.EditUnitCommand, RelativeSource={RelativeSource AncestorType=UserControl}}"
                                                CommandParameter="{Binding}"
                                                Style="{StaticResource SecondaryButton}"
                                                Padding="8,4" Margin="0,0,5,0"
                                                ToolTip="Düzenle"/>
                                        <Button Content="🗑️" 
                                                Command="{Binding DataContext.DeleteUnitCommand, RelativeSource={RelativeSource AncestorType=UserControl}}"
                                                CommandParameter="{Binding}"
                                                Style="{StaticResource SecondaryButton}"
                                                Padding="8,4"
                                                ToolTip="Sil"/>
                                    </StackPanel>
                                </DataTemplate>
                            </DataGridTemplateColumn.CellTemplate>
                        </DataGridTemplateColumn>
                    </DataGrid.Columns>
                </DataGrid>

                <!-- Empty State -->
                <StackPanel Grid.Row="1"
                            Visibility="{Binding FilteredUnits.Count, Converter={x:Static converters:EmptyStateConverter.Instance}}"
                            VerticalAlignment="Center" HorizontalAlignment="Center">
                    <TextBlock Text="🏠" FontSize="48" HorizontalAlignment="Center" Opacity="0.3"/>
                    <TextBlock Text="Henüz daire eklenmemiş" 
                               FontSize="16" HorizontalAlignment="Center" 
                               Margin="0,10,0,5" Opacity="0.6"/>
                    <TextBlock Text="İlk daireyi eklemek için 'Yeni Daire' butonuna tıklayın" 
                               FontSize="12" HorizontalAlignment="Center" 
                               Opacity="0.5"/>
                </StackPanel>
            </Grid>
        </Border>
    </Grid>
</UserControl>
