using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using ApartmanYonetimSistemi.Services;
using System.Windows.Media;
using System.Windows;

namespace ApartmanYonetimSistemi.ViewModels
{
    public partial class ForgotPasswordViewModel : BaseViewModel
    {
        private readonly IUserService _userService;

        [ObservableProperty]
        private string _email = string.Empty;

        [ObservableProperty]
        private string _resetCode = string.Empty;

        [ObservableProperty]
        private string _newPassword = string.Empty;

        [ObservableProperty]
        private string _confirmPassword = string.Empty;

        [ObservableProperty]
        private string _statusMessage = string.Empty;

        [ObservableProperty]
        private Brush _statusColor = Brushes.Black;

        [ObservableProperty]
        private bool _isCodeSent = false;

        [ObservableProperty]
        private bool _isCodeVerified = false;

        public event Action? RequestClose;

        public ForgotPasswordViewModel(IUserService userService)
        {
            _userService = userService;
            Title = "Şifremi Unuttum";
        }

        [RelayCommand]
        private async Task SendResetCode()
        {
            if (string.IsNullOrWhiteSpace(Email))
            {
                StatusMessage = "Lütfen e-posta adresinizi giriniz.";
                StatusColor = Brushes.Red;
                return;
            }

            if (!IsValidEmail(Email))
            {
                StatusMessage = "Lütfen geçerli bir e-posta adresi giriniz.";
                StatusColor = Brushes.Red;
                return;
            }

            IsBusy = true;
            StatusMessage = "Sıfırlama kodu gönderiliyor...";
            StatusColor = Brushes.Black;

            try
            {
                var result = await _userService.SendPasswordResetCodeAsync(Email);

                if (result)
                {
                    StatusMessage = "Sıfırlama kodu e-posta adresinize gönderildi. Lütfen e-postanızı kontrol edin.";
                    StatusColor = Brushes.Green;
                    IsCodeSent = true;
                }
                else
                {
                    StatusMessage = "Bu e-posta adresi ile kayıtlı bir kullanıcı bulunamadı.";
                    StatusColor = Brushes.Red;
                }
            }
            catch (Exception ex)
            {
                StatusMessage = "Hata: " + ex.Message;
                StatusColor = Brushes.Red;
            }
            finally
            {
                IsBusy = false;
            }
        }

        [RelayCommand]
        private async Task VerifyCode()
        {
            if (string.IsNullOrWhiteSpace(ResetCode))
            {
                StatusMessage = "Lütfen sıfırlama kodunu giriniz.";
                StatusColor = Brushes.Red;
                return;
            }

            if (ResetCode.Length != 6)
            {
                StatusMessage = "Sıfırlama kodu 6 haneli olmalıdır.";
                StatusColor = Brushes.Red;
                return;
            }

            IsBusy = true;
            StatusMessage = "Kod doğrulanıyor...";
            StatusColor = Brushes.Black;

            try
            {
                var result = await _userService.VerifyResetCodeAsync(Email, ResetCode);

                if (result)
                {
                    StatusMessage = "Kod doğrulandı. Yeni şifrenizi belirleyebilirsiniz.";
                    StatusColor = Brushes.Green;
                    IsCodeVerified = true;
                }
                else
                {
                    StatusMessage = "Geçersiz veya süresi dolmuş kod. Lütfen yeni kod talep edin.";
                    StatusColor = Brushes.Red;
                }
            }
            catch (Exception ex)
            {
                StatusMessage = "Hata: " + ex.Message;
                StatusColor = Brushes.Red;
            }
            finally
            {
                IsBusy = false;
            }
        }

        [RelayCommand]
        private async Task ResetPassword()
        {
            if (string.IsNullOrWhiteSpace(NewPassword))
            {
                StatusMessage = "Lütfen yeni şifrenizi giriniz.";
                StatusColor = Brushes.Red;
                return;
            }

            if (NewPassword.Length < 6)
            {
                StatusMessage = "Şifre en az 6 karakter olmalıdır.";
                StatusColor = Brushes.Red;
                return;
            }

            if (NewPassword != ConfirmPassword)
            {
                StatusMessage = "Şifreler eşleşmiyor.";
                StatusColor = Brushes.Red;
                return;
            }

            IsBusy = true;
            StatusMessage = "Şifre sıfırlanıyor...";
            StatusColor = Brushes.Black;

            try
            {
                var result = await _userService.ResetPasswordAsync(Email, ResetCode, NewPassword);

                if (result)
                {
                    StatusMessage = "Şifreniz başarıyla sıfırlandı. Giriş sayfasına yönlendiriliyorsunuz...";
                    StatusColor = Brushes.Green;

                    // 2 saniye bekle ve pencereyi kapat
                    await Task.Delay(2000);
                    RequestClose?.Invoke();
                }
                else
                {
                    StatusMessage = "Şifre sıfırlama işlemi başarısız. Lütfen tekrar deneyin.";
                    StatusColor = Brushes.Red;
                }
            }
            catch (Exception ex)
            {
                StatusMessage = "Hata: " + ex.Message;
                StatusColor = Brushes.Red;
            }
            finally
            {
                IsBusy = false;
            }
        }

        [RelayCommand]
        private void BackToLogin()
        {
            RequestClose?.Invoke();
        }

        private bool IsValidEmail(string email)
        {
            try
            {
                var addr = new System.Net.Mail.MailAddress(email);
                return addr.Address == email;
            }
            catch
            {
                return false;
            }
        }
    }
}
