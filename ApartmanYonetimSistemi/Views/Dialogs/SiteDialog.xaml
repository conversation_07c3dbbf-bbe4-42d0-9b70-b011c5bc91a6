<Window x:Class="ApartmanYonetimSistemi.Views.Dialogs.SiteDialog"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:converters="clr-namespace:ApartmanYonetimSistemi.Converters"
        Title="{Binding Title}"
        Height="600" Width="500"
        WindowStartupLocation="CenterOwner"
        ResizeMode="NoResize"
        Background="#F5F5F5">

    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <Border Grid.Row="0" Style="{StaticResource CardStyle}" Margin="0,0,0,20">
            <StackPanel>
                <TextBlock Text="{Binding Title}" Style="{StaticResource TitleStyle}"/>
                <TextBlock Text="Site bilgilerini girin" 
                           Style="{StaticResource SubtitleStyle}" Opacity="0.7"/>
            </StackPanel>
        </Border>

        <!-- Form -->
        <Border Grid.Row="1" Style="{StaticResource CardStyle}" Margin="0,0,0,20">
            <ScrollViewer VerticalScrollBarVisibility="Auto">
                <StackPanel>
                    <!-- Site Name -->
                    <StackPanel Margin="0,0,0,15">
                        <TextBlock Text="Site Adı *" FontWeight="SemiBold" Margin="0,0,0,5"/>
                        <TextBox Text="{Binding SiteName, UpdateSourceTrigger=PropertyChanged}"
                                 Height="35" Padding="10,0"
                                 BorderBrush="{Binding SiteNameError, Converter={x:Static converters:StringToVisibilityConverter.Instance}}"/>
                        <TextBlock Text="{Binding SiteNameError}" 
                                   Foreground="Red" FontSize="12" Margin="0,2,0,0"
                                   Visibility="{Binding SiteNameError, Converter={x:Static converters:StringToVisibilityConverter.Instance}}"/>
                    </StackPanel>

                    <!-- Address -->
                    <StackPanel Margin="0,0,0,15">
                        <TextBlock Text="Adres *" FontWeight="SemiBold" Margin="0,0,0,5"/>
                        <TextBox Text="{Binding Address, UpdateSourceTrigger=PropertyChanged}"
                                 Height="35" Padding="10,0"/>
                        <TextBlock Text="{Binding AddressError}" 
                                   Foreground="Red" FontSize="12" Margin="0,2,0,0"
                                   Visibility="{Binding AddressError, Converter={x:Static converters:StringToVisibilityConverter.Instance}}"/>
                    </StackPanel>

                    <!-- City and District -->
                    <Grid Margin="0,0,0,15">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="10"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>

                        <!-- City -->
                        <StackPanel Grid.Column="0">
                            <TextBlock Text="Şehir *" FontWeight="SemiBold" Margin="0,0,0,5"/>
                            <ComboBox SelectedValue="{Binding City}" Height="35" IsEditable="True">
                                <ComboBoxItem Content="İstanbul"/>
                                <ComboBoxItem Content="Ankara"/>
                                <ComboBoxItem Content="İzmir"/>
                                <ComboBoxItem Content="Bursa"/>
                                <ComboBoxItem Content="Antalya"/>
                                <ComboBoxItem Content="Adana"/>
                                <ComboBoxItem Content="Konya"/>
                                <ComboBoxItem Content="Gaziantep"/>
                                <ComboBoxItem Content="Kayseri"/>
                                <ComboBoxItem Content="Mersin"/>
                            </ComboBox>
                        </StackPanel>

                        <!-- District -->
                        <StackPanel Grid.Column="2">
                            <TextBlock Text="İlçe *" FontWeight="SemiBold" Margin="0,0,0,5"/>
                            <TextBox Text="{Binding District, UpdateSourceTrigger=PropertyChanged}"
                                     Height="35" Padding="10,0"/>
                        </StackPanel>
                    </Grid>

                    <!-- Postal Code -->
                    <StackPanel Margin="0,0,0,15">
                        <TextBlock Text="Posta Kodu" FontWeight="SemiBold" Margin="0,0,0,5"/>
                        <TextBox Text="{Binding PostalCode, UpdateSourceTrigger=PropertyChanged}"
                                 Height="35" Padding="10,0" MaxLength="5"/>
                    </StackPanel>

                    <!-- Company (for SuperAdmin only) -->
                    <StackPanel Margin="0,0,0,15" 
                                Visibility="{Binding ShowCompanySelection, Converter={x:Static converters:BooleanToVisibilityConverter.Instance}}">
                        <TextBlock Text="Şirket *" FontWeight="SemiBold" Margin="0,0,0,5"/>
                        <ComboBox ItemsSource="{Binding Companies}"
                                  SelectedValue="{Binding SelectedCompanyId}"
                                  SelectedValuePath="Id"
                                  DisplayMemberPath="Name"
                                  Height="35"/>
                    </StackPanel>

                    <!-- Description -->
                    <StackPanel Margin="0,0,0,15">
                        <TextBlock Text="Açıklama" FontWeight="SemiBold" Margin="0,0,0,5"/>
                        <TextBox Text="{Binding Description, UpdateSourceTrigger=PropertyChanged}"
                                 Height="80" Padding="10,5"
                                 TextWrapping="Wrap"
                                 AcceptsReturn="True"
                                 VerticalScrollBarVisibility="Auto"/>
                    </StackPanel>
                </StackPanel>
            </ScrollViewer>
        </Border>

        <!-- Buttons -->
        <Border Grid.Row="2" Style="{StaticResource CardStyle}">
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Right">
                <Button Content="İptal" 
                        Style="{StaticResource SecondaryButton}"
                        Command="{Binding CancelCommand}"
                        Margin="0,0,10,0" 
                        Padding="20,10" 
                        MinWidth="80"/>
                <Button Content="{Binding SaveButtonText}" 
                        Style="{StaticResource PrimaryButton}"
                        Command="{Binding SaveCommand}"
                        Padding="20,10" 
                        MinWidth="80"/>
            </StackPanel>
        </Border>

        <!-- Loading Overlay -->
        <Border Grid.RowSpan="3" 
                Background="#80000000" 
                Visibility="{Binding IsBusy, Converter={x:Static converters:BooleanToVisibilityConverter.Instance}}">
            <StackPanel VerticalAlignment="Center" HorizontalAlignment="Center">
                <ProgressBar IsIndeterminate="True" Width="200" Height="4" Margin="0,0,0,10"/>
                <TextBlock Text="Kaydediliyor..." Foreground="White" FontSize="14" HorizontalAlignment="Center"/>
            </StackPanel>
        </Border>
    </Grid>
</Window>
