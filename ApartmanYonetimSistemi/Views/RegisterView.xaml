<Window x:Class="ApartmanYonetimSistemi.Views.RegisterView"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="Kayıt Ol - Apartman Yönet<PERSON>" 
        Height="600" Width="450"
        WindowStartupLocation="CenterScreen"
        ResizeMode="NoResize"
        Background="#f5f5f5">

    <Window.Resources>
        <Style x:Key="ModernButton" TargetType="Button">
            <Setter Property="Background" Value="#007ACC"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Padding" Value="15,10"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}" 
                                CornerRadius="5"
                                Padding="{TemplateBinding Padding}">
                            <ContentPresenter HorizontalAlignment="Center" 
                                            VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="#005a9e"/>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter Property="Background" Value="#004578"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <Style x:Key="SecondaryButton" TargetType="Button" BasedOn="{StaticResource ModernButton}">
            <Setter Property="Background" Value="#6c757d"/>
            <Style.Triggers>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="Background" Value="#5a6268"/>
                </Trigger>
            </Style.Triggers>
        </Style>

        <Style x:Key="ModernTextBox" TargetType="TextBox">
            <Setter Property="Padding" Value="12,10"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="BorderBrush" Value="#ddd"/>
            <Setter Property="Background" Value="White"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="TextBox">
                        <Border Background="{TemplateBinding Background}" 
                                BorderBrush="{TemplateBinding BorderBrush}"
                                BorderThickness="{TemplateBinding BorderThickness}"
                                CornerRadius="5"
                                Padding="{TemplateBinding Padding}">
                            <ScrollViewer x:Name="PART_ContentHost"/>
                        </Border>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <Style x:Key="ModernPasswordBox" TargetType="PasswordBox">
            <Setter Property="Padding" Value="12,10"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="BorderBrush" Value="#ddd"/>
            <Setter Property="Background" Value="White"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="PasswordBox">
                        <Border Background="{TemplateBinding Background}" 
                                BorderBrush="{TemplateBinding BorderBrush}"
                                BorderThickness="{TemplateBinding BorderThickness}"
                                CornerRadius="5"
                                Padding="{TemplateBinding Padding}">
                            <ScrollViewer x:Name="PART_ContentHost"/>
                        </Border>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
    </Window.Resources>

    <Grid>
        <Border Background="White" 
                CornerRadius="10" 
                Margin="30"
                Effect="{DynamicResource {x:Static SystemParameters.DropShadowKey}}">
            
            <StackPanel Margin="40" VerticalAlignment="Center">
                <!-- Logo/Title -->
                <TextBlock Text="🏢" 
                          FontSize="48" 
                          HorizontalAlignment="Center" 
                          Margin="0,0,0,10"/>
                
                <TextBlock Text="Kayıt Ol" 
                          FontSize="28" 
                          FontWeight="Bold" 
                          HorizontalAlignment="Center" 
                          Foreground="#333" 
                          Margin="0,0,0,30"/>

                <!-- Form Fields -->
                <TextBlock Text="Ad:" FontWeight="SemiBold" Margin="0,0,0,5"/>
                <TextBox x:Name="FirstNameTextBox" 
                         Text="{Binding FirstName, UpdateSourceTrigger=PropertyChanged}"
                         Style="{StaticResource ModernTextBox}" 
                         Margin="0,0,0,15"/>

                <TextBlock Text="Soyad:" FontWeight="SemiBold" Margin="0,0,0,5"/>
                <TextBox x:Name="LastNameTextBox" 
                         Text="{Binding LastName, UpdateSourceTrigger=PropertyChanged}"
                         Style="{StaticResource ModernTextBox}" 
                         Margin="0,0,0,15"/>

                <TextBlock Text="E-posta:" FontWeight="SemiBold" Margin="0,0,0,5"/>
                <TextBox x:Name="EmailTextBox" 
                         Text="{Binding Email, UpdateSourceTrigger=PropertyChanged}"
                         Style="{StaticResource ModernTextBox}" 
                         Margin="0,0,0,15"/>

                <TextBlock Text="Şifre:" FontWeight="SemiBold" Margin="0,0,0,5"/>
                <PasswordBox x:Name="PasswordBox" 
                             Style="{StaticResource ModernPasswordBox}" 
                             Margin="0,0,0,15"/>

                <TextBlock Text="Şifre Tekrar:" FontWeight="SemiBold" Margin="0,0,0,5"/>
                <PasswordBox x:Name="ConfirmPasswordBox" 
                             Style="{StaticResource ModernPasswordBox}" 
                             Margin="0,0,0,20"/>

                <!-- Status Message -->
                <TextBlock Text="{Binding StatusMessage}" 
                          Foreground="{Binding StatusColor}"
                          FontWeight="SemiBold"
                          HorizontalAlignment="Center" 
                          Margin="0,0,0,20"
                          TextWrapping="Wrap"/>

                <!-- Buttons -->
                <Button Content="Kayıt Ol" 
                        Style="{StaticResource ModernButton}"
                        Command="{Binding RegisterCommand}"
                        CommandParameter="{Binding RelativeSource={RelativeSource AncestorType=Window}}"
                        Margin="0,0,0,15"/>

                <Button Content="Giriş Sayfasına Dön" 
                        Style="{StaticResource SecondaryButton}"
                        Command="{Binding BackToLoginCommand}"
                        Margin="0,0,0,10"/>
            </StackPanel>
        </Border>
    </Grid>
</Window>
