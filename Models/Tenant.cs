using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace ApartmanYonetimSistemi.Models
{
    public class Tenant : BaseEntity
    {
        [Required]
        [StringLength(100)]
        public string FirstName { get; set; } = string.Empty;

        [Required]
        [StringLength(100)]
        public string LastName { get; set; } = string.Empty;

        [StringLength(255)]
        [EmailAddress]
        public string Email { get; set; } = string.Empty;

        [StringLength(20)]
        public string PhoneNumber { get; set; } = string.Empty;

        [StringLength(20)]
        public string NationalId { get; set; } = string.Empty;

        public DateTime? DateOfBirth { get; set; }

        [StringLength(200)]
        public string Occupation { get; set; } = string.Empty;

        [StringLength(200)]
        public string EmergencyContactName { get; set; } = string.Empty;

        [StringLength(20)]
        public string EmergencyContactPhone { get; set; } = string.Empty;

        [StringLength(50)]
        public string? UnitId { get; set; }

        public DateTime? LeaseStartDate { get; set; }

        public DateTime? LeaseEndDate { get; set; }

        [Column(TypeName = "decimal(18,2)")]
        public decimal MonthlyRent { get; set; } = 0;

        [Column(TypeName = "decimal(18,2)")]
        public decimal DepositPaid { get; set; } = 0;

        [Required]
        public TenantStatus Status { get; set; } = TenantStatus.Active;

        [StringLength(1000)]
        public string Notes { get; set; } = string.Empty;

        [StringLength(50)]
        public string CompanyId { get; set; } = string.Empty;

        // Navigation properties
        public virtual Unit? Unit { get; set; }
        public virtual ICollection<Payment> Payments { get; set; } = new List<Payment>();

        // Computed properties
        [NotMapped]
        public string FullName => $"{FirstName} {LastName}".Trim();

        [NotMapped]
        public string DisplayName => !string.IsNullOrEmpty(FullName) ? FullName : Email;

        [NotMapped]
        public bool IsLeaseActive => LeaseEndDate.HasValue && LeaseEndDate.Value > DateTime.UtcNow;

        [NotMapped]
        public int Age => DateOfBirth.HasValue ? DateTime.UtcNow.Year - DateOfBirth.Value.Year : 0;
    }
}
