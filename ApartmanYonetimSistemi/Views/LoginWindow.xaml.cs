using System.Windows;
using System.Windows.Controls;
using ApartmanYonetimSistemi.ViewModels;
using Microsoft.Extensions.DependencyInjection;

namespace ApartmanYonetimSistemi.Views
{
    public partial class LoginWindow : Window
    {
        public LoginWindow()
        {
            InitializeComponent();

            // ViewModel'i DI container'dan al
            var app = (App)Application.Current;
            DataContext = app.Services.GetRequiredService<LoginViewModel>();
        }

        private void PasswordBox_PasswordChanged(object sender, RoutedEventArgs e)
        {
            // PasswordBox binding için gere<PERSON> de<PERSON>, Command kullanıyoruz
        }

        private void RegisterLink_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var app = (App)Application.Current;
                var registerWindow = app.Services.GetRequiredService<Views.RegisterWindow>();

                registerWindow.Show();
                this.Close();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Kayıt sayfası açılırken hata oluştu: {ex.Message}", "Hata", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void ForgotPassword_Click(object sender, RoutedEventArgs e)
        {
            var app = (App)Application.Current;
            var forgotWindow = app.Services.GetRequiredService<Views.ForgotPasswordWindow>();
            forgotWindow.Show();
        }
    }
}
