namespace ApartmanYonetimSistemi.Models
{
    public enum UserRole
    {
        <PERSON><PERSON><PERSON><PERSON>,
        <PERSON><PERSON>,
        Manager,
        Tenant,
        Owner
    }

    public enum UserStatus
    {
        Active,
        Inactive,
        Pending,
        Suspended
    }

    public enum UnitType
    {
        Apartment,
        Office,
        Shop,
        Storage,
        Parking
    }

    public enum UnitStatus
    {
        Available,
        Occupied,
        Maintenance,
        Reserved
    }

    public enum TenantStatus
    {
        Active,
        MovedOut,
        Pending,
        Suspended
    }

    public enum PaymentType
    {
        Rent,
        Maintenance,
        Utilities,
        Deposit,
        Fine,
        Other
    }

    public enum PaymentStatus
    {
        Pending,
        Paid,
        Overdue,
        Cancelled,
        Refunded
    }

    public enum PaymentMethod
    {
        Cash,
        BankTransfer,
        CreditCard,
        Check,
        Online
    }
}
