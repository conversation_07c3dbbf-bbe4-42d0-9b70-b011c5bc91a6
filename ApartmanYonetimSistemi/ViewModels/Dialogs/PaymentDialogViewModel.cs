using System.Collections.ObjectModel;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using ApartmanYonetimSistemi.Models;
using ApartmanYonetimSistemi.Services;
using System.Threading.Tasks;
using System.Windows;

namespace ApartmanYonetimSistemi.ViewModels.Dialogs
{
    public partial class PaymentDialogViewModel : BaseViewModel
    {
        private readonly IPaymentService _paymentService;
        private readonly IUnitService _unitService;
        private readonly ITenantService _tenantService;
        private readonly User _currentUser;
        private Payment? _editingPayment;

        [ObservableProperty]
        private string _description = string.Empty;

        [ObservableProperty]
        private string? _selectedUnitId;

        [ObservableProperty]
        private string? _selectedTenantId;

        [ObservableProperty]
        private PaymentType _selectedType = PaymentType.Rent;

        [ObservableProperty]
        private PaymentStatus _selectedStatus = PaymentStatus.Pending;

        [ObservableProperty]
        private decimal _amount = 0;

        [ObservableProperty]
        private DateTime _dueDate = DateTime.Today;

        [ObservableProperty]
        private DateTime? _paidDate;

        [ObservableProperty]
        private string _notes = string.Empty;

        [ObservableProperty]
        private ObservableCollection<Unit> _units = new();

        [ObservableProperty]
        private ObservableCollection<Tenant> _tenants = new();

        [ObservableProperty]
        private string _saveButtonText = "Kaydet";

        // Validation Errors
        [ObservableProperty]
        private string _descriptionError = string.Empty;

        [ObservableProperty]
        private string _amountError = string.Empty;

        public bool DialogResult { get; private set; }

        public PaymentDialogViewModel(IPaymentService paymentService, IUnitService unitService, ITenantService tenantService, User currentUser, Payment? editingPayment = null)
        {
            _paymentService = paymentService;
            _unitService = unitService;
            _tenantService = tenantService;
            _currentUser = currentUser;
            _editingPayment = editingPayment;

            Title = editingPayment == null ? "Yeni Ödeme Ekle" : "Ödeme Düzenle";
            SaveButtonText = editingPayment == null ? "Kaydet" : "Güncelle";

            if (editingPayment != null)
            {
                LoadPaymentData();
            }

            _ = LoadDataAsync();
        }

        private void LoadPaymentData()
        {
            if (_editingPayment == null) return;

            Description = _editingPayment.Description;
            SelectedUnitId = _editingPayment.UnitId;
            SelectedTenantId = _editingPayment.TenantId;
            SelectedType = _editingPayment.Type;
            SelectedStatus = _editingPayment.Status;
            Amount = _editingPayment.Amount;
            DueDate = _editingPayment.DueDate;
            PaidDate = _editingPayment.PaidDate;
            Notes = _editingPayment.Notes ?? string.Empty;
        }

        private async Task LoadDataAsync()
        {
            try
            {
                IEnumerable<Unit> unitList;
                IEnumerable<Tenant> tenantList;

                if (_currentUser.Role == UserRole.SuperAdmin)
                {
                    unitList = await _unitService.GetAllAsync();
                    tenantList = await _tenantService.GetAllAsync();
                }
                else if (_currentUser.Role == UserRole.Admin)
                {
                    unitList = await _unitService.GetByCompanyIdAsync(_currentUser.CompanyId);
                    tenantList = await _tenantService.GetByCompanyIdAsync(_currentUser.CompanyId);
                }
                else
                {
                    unitList = await _unitService.GetByUserIdAsync(_currentUser.Id);
                    tenantList = await _tenantService.GetByUserIdAsync(_currentUser.Id);
                }

                Units.Clear();
                foreach (var unit in unitList.OrderBy(u => u.UnitNumber))
                {
                    Units.Add(unit);
                }

                Tenants.Clear();
                foreach (var tenant in tenantList.OrderBy(t => t.FirstName).ThenBy(t => t.LastName))
                {
                    Tenants.Add(tenant);
                }
            }
            catch (Exception ex)
            {
                // Handle error
            }
        }

        [RelayCommand]
        private async Task Save()
        {
            if (!ValidateForm()) return;

            try
            {
                IsBusy = true;

                var payment = _editingPayment ?? new Payment { Id = Guid.NewGuid().ToString() };
                
                payment.Description = Description.Trim();
                payment.UnitId = SelectedUnitId!;
                payment.TenantId = SelectedTenantId;
                payment.Type = SelectedType;
                payment.Status = SelectedStatus;
                payment.Amount = Amount;
                payment.DueDate = DueDate;
                payment.PaidDate = PaidDate;
                payment.Notes = string.IsNullOrWhiteSpace(Notes) ? null : Notes.Trim();
                payment.UserId = _currentUser.Id;

                if (_editingPayment == null)
                {
                    await _paymentService.CreateAsync(payment);
                }
                else
                {
                    await _paymentService.UpdateAsync(payment);
                }

                DialogResult = true;
                
                // Close dialog
                if (Application.Current.MainWindow?.OwnedWindows.Count > 0)
                {
                    var dialog = Application.Current.MainWindow.OwnedWindows[^1];
                    dialog.DialogResult = true;
                    dialog.Close();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Ödeme kaydedilirken hata oluştu: {ex.Message}", "Hata", MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                IsBusy = false;
            }
        }

        [RelayCommand]
        private void Cancel()
        {
            DialogResult = false;
            
            // Close dialog
            if (Application.Current.MainWindow?.OwnedWindows.Count > 0)
            {
                var dialog = Application.Current.MainWindow.OwnedWindows[^1];
                dialog.DialogResult = false;
                dialog.Close();
            }
        }

        private bool ValidateForm()
        {
            bool isValid = true;

            // Description validation
            if (string.IsNullOrWhiteSpace(Description))
            {
                DescriptionError = "Açıklama gereklidir";
                isValid = false;
            }
            else if (Description.Trim().Length < 3)
            {
                DescriptionError = "Açıklama en az 3 karakter olmalıdır";
                isValid = false;
            }
            else
            {
                DescriptionError = string.Empty;
            }

            // Amount validation
            if (Amount <= 0)
            {
                AmountError = "Tutar 0'dan büyük olmalıdır";
                isValid = false;
            }
            else
            {
                AmountError = string.Empty;
            }

            return isValid;
        }
    }
}
