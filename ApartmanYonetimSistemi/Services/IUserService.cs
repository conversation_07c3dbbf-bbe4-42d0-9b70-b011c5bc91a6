using ApartmanYonetimSistemi.Models;

namespace ApartmanYonetimSistemi.Services
{
    public interface IUserService
    {
        Task<User?> AuthenticateAsync(string email, string password);
        Task<bool> CreateUserAsync(User user, string password);
        Task<User?> GetUserByEmailAsync(string email);
        Task<bool> UpdateUserAsync(User user);
        Task<bool> ChangePasswordAsync(string userId, string oldPassword, string newPassword);

        // <PERSON><PERSON>re sıfırlama metodları
        Task<bool> SendPasswordResetCodeAsync(string email);
        Task<bool> VerifyResetCodeAsync(string email, string resetCode);
        Task<bool> ResetPasswordAsync(string email, string resetCode, string newPassword);

        // Admin panel için ek metodlar
        Task<IEnumerable<User>> GetAllAsync();
        Task<IEnumerable<User>> GetByCompanyIdAsync(string? companyId);
        Task<User?> GetByIdAsync(string id);
        Task<User> UpdateAsync(User user);
        Task DeleteAsync(string id);
    }
}
