using ApartmanYonetimSistemi.Models;
using Microsoft.EntityFrameworkCore;
using BCrypt.Net;

namespace ApartmanYonetimSistemi.Data
{
    public static class DataSeeder
    {
        public static async Task SeedAsync(ApartmanDbContext context)
        {
            // Eğer veri varsa seed etme
            if (await context.Users.AnyAsync())
                return;

            // 1. Şirketler
            var company1 = new Company
            {
                Id = Guid.NewGuid().ToString(),
                Name = "Test Emlak A.Ş.",
                Address = "İstanbul, Türkiye",
                PhoneNumber = "+90 ************",
                Email = "<EMAIL>",
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow,
                IsActive = true
            };

            var company2 = new Company
            {
                Id = Guid.NewGuid().ToString(),
                Name = "Mega Emlak Ltd.",
                Address = "Ankara, Türkiye",
                PhoneNumber = "+90 ************",
                Email = "<EMAIL>",
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow,
                IsActive = true
            };

            context.Companies.AddRange(company1, company2);
            await context.SaveChangesAsync();

            // 2. <PERSON><PERSON>ılar
            var superAdmin = new User
            {
                Id = Guid.NewGuid().ToString(),
                FirstName = "Super",
                LastName = "Admin",
                Email = "<EMAIL>",
                PasswordHash = BCrypt.Net.BCrypt.HashPassword("admin123"),
                Role = UserRole.SuperAdmin,
                CompanyId = null,
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow,
                IsActive = true
            };

            var admin1 = new User
            {
                Id = Guid.NewGuid().ToString(),
                FirstName = "Ahmet",
                LastName = "Yönetici",
                Email = "<EMAIL>",
                PasswordHash = BCrypt.Net.BCrypt.HashPassword("admin123"),
                Role = UserRole.Admin,
                CompanyId = company1.Id,
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow,
                IsActive = true
            };

            var realtor1 = new User
            {
                Id = Guid.NewGuid().ToString(),
                FirstName = "Mehmet",
                LastName = "Emlakçı",
                Email = "<EMAIL>",
                PasswordHash = BCrypt.Net.BCrypt.HashPassword("123456"),
                Role = UserRole.Realtor,
                CompanyId = company1.Id,
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow,
                IsActive = true
            };

            var user1 = new User
            {
                Id = Guid.NewGuid().ToString(),
                FirstName = "Ali",
                LastName = "Kullanıcı",
                Email = "<EMAIL>",
                PasswordHash = BCrypt.Net.BCrypt.HashPassword("123456"),
                Role = UserRole.User,
                CompanyId = null,
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow,
                IsActive = true
            };

            context.Users.AddRange(superAdmin, admin1, realtor1, user1);
            await context.SaveChangesAsync();

            // 3. Siteler
            var site1 = new Site
            {
                Id = Guid.NewGuid().ToString(),
                Name = "Güneş Sitesi",
                Address = "Ataşehir, İstanbul",
                City = "İstanbul",
                District = "Ataşehir",
                PostalCode = "34750",
                CompanyId = company1.Id,
                UserId = admin1.Id,
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow,
                IsActive = true
            };

            var site2 = new Site
            {
                Id = Guid.NewGuid().ToString(),
                Name = "Yıldız Rezidans",
                Address = "Çankaya, Ankara",
                City = "Ankara",
                District = "Çankaya",
                PostalCode = "06100",
                CompanyId = company1.Id,
                UserId = realtor1.Id,
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow,
                IsActive = true
            };

            context.Sites.AddRange(site1, site2);
            await context.SaveChangesAsync();

            // 4. Apartmanlar
            var apartment1 = new Apartment
            {
                Id = Guid.NewGuid().ToString(),
                Name = "A Blok",
                SiteId = site1.Id,
                FloorCount = 8,
                UnitCount = 32,
                BuildYear = 2018,
                Description = "Modern apartman",
                UserId = admin1.Id,
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow,
                IsActive = true
            };

            var apartment2 = new Apartment
            {
                Id = Guid.NewGuid().ToString(),
                Name = "B Blok",
                SiteId = site1.Id,
                FloorCount = 6,
                UnitCount = 24,
                BuildYear = 2019,
                Description = "Lüks apartman",
                UserId = admin1.Id,
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow,
                IsActive = true
            };

            context.Apartments.AddRange(apartment1, apartment2);
            await context.SaveChangesAsync();

            // 5. Daireler
            var units = new List<Unit>();
            for (int floor = 1; floor <= 8; floor++)
            {
                for (int unitNum = 1; unitNum <= 4; unitNum++)
                {
                    var unit = new Unit
                    {
                        Id = Guid.NewGuid().ToString(),
                        UnitNumber = $"{floor}{unitNum:D2}",
                        ApartmentId = apartment1.Id,
                        Floor = floor,
                        Type = UnitType.Apartment,
                        RoomCount = unitNum <= 2 ? 2 : 3,
                        Area = unitNum <= 2 ? 85 : 120,
                        RentPrice = unitNum <= 2 ? 3500 : 4500,
                        Status = floor <= 6 ? UnitStatus.Rented : UnitStatus.Available,
                        UserId = admin1.Id,
                        CreatedAt = DateTime.UtcNow,
                        UpdatedAt = DateTime.UtcNow,
                        IsActive = true
                    };
                    units.Add(unit);
                }
            }

            context.Units.AddRange(units);
            await context.SaveChangesAsync();

            // 6. Kiracılar
            var tenant1 = new Tenant
            {
                Id = Guid.NewGuid().ToString(),
                FirstName = "Ayşe",
                LastName = "Demir",
                PhoneNumber = "+90 ************",
                Email = "<EMAIL>",
                CurrentUnitId = units.First().Id,
                MoveInDate = DateTime.Now.AddMonths(-6),
                Status = TenantStatus.Active,
                UserId = admin1.Id,
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow,
                IsActive = true
            };

            var tenant2 = new Tenant
            {
                Id = Guid.NewGuid().ToString(),
                FirstName = "Fatma",
                LastName = "Kaya",
                PhoneNumber = "+90 ************",
                Email = "<EMAIL>",
                CurrentUnitId = units.Skip(1).First().Id,
                MoveInDate = DateTime.Now.AddMonths(-3),
                Status = TenantStatus.Active,
                UserId = admin1.Id,
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow,
                IsActive = true
            };

            context.Tenants.AddRange(tenant1, tenant2);
            await context.SaveChangesAsync();

            // 7. Ödemeler
            var payments = new List<Payment>();
            var rentedUnits = units.Where(u => u.Status == UnitStatus.Rented).Take(10).ToList();

            foreach (var unit in rentedUnits)
            {
                // Son 3 ayın kira ödemeleri
                for (int month = 0; month < 3; month++)
                {
                    var payment = new Payment
                    {
                        Id = Guid.NewGuid().ToString(),
                        UnitId = unit.Id,
                        TenantId = month < 2 ? tenant1.Id : tenant2.Id,
                        Type = PaymentType.Rent,
                        Amount = unit.RentPrice ?? 0,
                        DueDate = DateTime.Now.AddMonths(-month).AddDays(-15),
                        PaidDate = month == 0 ? null : DateTime.Now.AddMonths(-month).AddDays(-10),
                        Status = month == 0 ? PaymentStatus.Pending : PaymentStatus.Paid,
                        Description = $"{DateTime.Now.AddMonths(-month):yyyy/MM} Kira Ödemesi",
                        UserId = admin1.Id,
                        CreatedAt = DateTime.UtcNow,
                        UpdatedAt = DateTime.UtcNow,
                        IsActive = true
                    };
                    payments.Add(payment);
                }
            }

            context.Payments.AddRange(payments);
            await context.SaveChangesAsync();
        }
    }
}
