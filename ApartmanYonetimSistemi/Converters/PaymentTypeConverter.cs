using System;
using System.Globalization;
using System.Windows.Data;
using ApartmanYonetimSistemi.Models;

namespace ApartmanYonetimSistemi.Converters
{
    public class PaymentTypeConverter : IValueConverter
    {
        public static PaymentTypeConverter Instance { get; } = new PaymentTypeConverter();

        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is PaymentType paymentType)
            {
                return paymentType switch
                {
                    PaymentType.Rent => "Kira",
                    PaymentType.Maintenance => "Aidat",
                    PaymentType.Utilities => "Faturalar",
                    PaymentType.Other => "Diğer",
                    _ => value.ToString()
                };
            }
            
            return value?.ToString() ?? string.Empty;
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
}
