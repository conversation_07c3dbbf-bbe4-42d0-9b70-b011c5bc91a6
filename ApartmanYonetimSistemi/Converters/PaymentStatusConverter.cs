using System;
using System.Globalization;
using System.Windows.Data;
using ApartmanYonetimSistemi.Models;

namespace ApartmanYonetimSistemi.Converters
{
    public class PaymentStatusConverter : IValueConverter
    {
        public static PaymentStatusConverter Instance { get; } = new PaymentStatusConverter();

        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is PaymentStatus paymentStatus)
            {
                return paymentStatus switch
                {
                    PaymentStatus.Pending => "Bekliyor",
                    PaymentStatus.Paid => "Ödendi",
                    PaymentStatus.Overdue => "Gecikmiş",
                    PaymentStatus.Cancelled => "İptal",
                    _ => value.ToString()
                };
            }
            
            return value?.ToString() ?? string.Empty;
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
}
