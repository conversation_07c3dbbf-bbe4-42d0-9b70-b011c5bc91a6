<UserControl x:Class="ApartmanYonetimSistemi.Views.TenantsView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:converters="clr-namespace:ApartmanYonetimSistemi.Converters">

    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <Border Grid.Row="0" Style="{StaticResource CardStyle}" Margin="0,0,0,10">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <StackPanel Grid.Column="0">
                    <TextBlock Text="👥 Kiracı Yönetimi" Style="{StaticResource TitleStyle}"/>
                    <TextBlock Text="Kiracıları görüntüleyin, ekleyin ve yönetin" 
                               Style="{StaticResource SubtitleStyle}" Opacity="0.7"/>
                </StackPanel>

                <StackPanel Grid.Column="1" Orientation="Horizontal">
                    <Button Content="➕ Yeni Kiracı" 
                            Style="{StaticResource PrimaryButton}"
                            Command="{Binding AddTenantCommand}"
                            Margin="0,0,10,0"/>
                    <Button Content="🔄 Yenile" 
                            Style="{StaticResource SecondaryButton}"
                            Command="{Binding RefreshDataCommand}"/>
                </StackPanel>
            </Grid>
        </Border>

        <!-- Filters -->
        <Border Grid.Row="1" Style="{StaticResource CardStyle}" Margin="0,0,0,10">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="2*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>

                <TextBlock Grid.Row="0" Grid.ColumnSpan="3" Text="Filtreler" 
                           Style="{StaticResource SubtitleStyle}" Margin="0,0,0,10"/>

                <!-- Search -->
                <StackPanel Grid.Row="1" Grid.Column="0" Margin="0,0,10,0">
                    <TextBlock Text="Arama" FontWeight="SemiBold" FontSize="12" Foreground="#666" Margin="0,0,0,2"/>
                    <TextBox Text="{Binding SearchText, UpdateSourceTrigger=PropertyChanged}"
                             Height="32" Padding="8,0"
                             ToolTip="Ad, soyad, telefon veya email..."/>
                </StackPanel>

                <!-- Status Filter -->
                <StackPanel Grid.Row="1" Grid.Column="1" Margin="0,0,10,0">
                    <TextBlock Text="Durum" FontWeight="SemiBold" FontSize="12" Foreground="#666" Margin="0,0,0,2"/>
                    <ComboBox SelectedValue="{Binding SelectedStatus}" Height="32">
                        <ComboBoxItem Content="Tümü" Tag="{x:Null}"/>
                        <ComboBoxItem Content="Aktif" Tag="Active"/>
                        <ComboBoxItem Content="Taşınmış" Tag="MovedOut"/>
                        <ComboBoxItem Content="Beklemede" Tag="Pending"/>
                        <ComboBoxItem Content="Askıda" Tag="Suspended"/>
                    </ComboBox>
                </StackPanel>

                <!-- Clear Filters -->
                <StackPanel Grid.Row="1" Grid.Column="2" VerticalAlignment="Bottom">
                    <Button Content="Temizle" 
                            Style="{StaticResource SecondaryButton}"
                            Command="{Binding ClearFiltersCommand}"
                            Padding="15,8"/>
                </StackPanel>
            </Grid>
        </Border>

        <!-- Tenants List -->
        <Border Grid.Row="2" Style="{StaticResource CardStyle}">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                </Grid.RowDefinitions>

                <!-- List Header -->
                <TextBlock Grid.Row="0" Text="Kiracılar" Style="{StaticResource SubtitleStyle}" Margin="0,0,0,10"/>

                <!-- Loading Indicator -->
                <ProgressBar Grid.Row="1" IsIndeterminate="True" 
                             Visibility="{Binding IsBusy, Converter={x:Static converters:BooleanToVisibilityConverter.Instance}}"
                             Height="4" VerticalAlignment="Top"/>

                <!-- Data Grid -->
                <DataGrid Grid.Row="1" 
                          ItemsSource="{Binding FilteredTenants}"
                          SelectedItem="{Binding SelectedTenant}"
                          AutoGenerateColumns="False"
                          CanUserAddRows="False"
                          CanUserDeleteRows="False"
                          IsReadOnly="True"
                          GridLinesVisibility="Horizontal"
                          HeadersVisibility="Column"
                          SelectionMode="Single"
                          AlternatingRowBackground="#F9F9F9">

                    <DataGrid.Columns>
                        <!-- Full Name -->
                        <DataGridTextColumn Header="Ad Soyad" Width="150">
                            <DataGridTextColumn.Binding>
                                <Binding Path="FullName"/>
                            </DataGridTextColumn.Binding>
                        </DataGridTextColumn>

                        <!-- Phone -->
                        <DataGridTextColumn Header="Telefon" Binding="{Binding PhoneNumber}" Width="120"/>

                        <!-- Email -->
                        <DataGridTextColumn Header="Email" Binding="{Binding Email}" Width="200"/>

                        <!-- Current Unit -->
                        <DataGridTextColumn Header="Daire" Width="120">
                            <DataGridTextColumn.Binding>
                                <Binding Path="CurrentUnit.UnitNumber"/>
                            </DataGridTextColumn.Binding>
                        </DataGridTextColumn>

                        <!-- Move In Date -->
                        <DataGridTextColumn Header="Giriş Tarihi" Width="100">
                            <DataGridTextColumn.Binding>
                                <Binding Path="MoveInDate" StringFormat="dd.MM.yyyy"/>
                            </DataGridTextColumn.Binding>
                        </DataGridTextColumn>

                        <!-- Status -->
                        <DataGridTextColumn Header="Durum" Width="100">
                            <DataGridTextColumn.Binding>
                                <Binding Path="Status"/>
                            </DataGridTextColumn.Binding>
                        </DataGridTextColumn>

                        <!-- Actions -->
                        <DataGridTemplateColumn Header="İşlemler" Width="200">
                            <DataGridTemplateColumn.CellTemplate>
                                <DataTemplate>
                                    <StackPanel Orientation="Horizontal">
                                        <Button Content="👁️" 
                                                Command="{Binding DataContext.ViewTenantDetailsCommand, RelativeSource={RelativeSource AncestorType=UserControl}}"
                                                CommandParameter="{Binding}"
                                                Style="{StaticResource SecondaryButton}"
                                                Padding="8,4" Margin="0,0,5,0"
                                                ToolTip="Detayları Görüntüle"/>
                                        <Button Content="✏️" 
                                                Command="{Binding DataContext.EditTenantCommand, RelativeSource={RelativeSource AncestorType=UserControl}}"
                                                CommandParameter="{Binding}"
                                                Style="{StaticResource SecondaryButton}"
                                                Padding="8,4" Margin="0,0,5,0"
                                                ToolTip="Düzenle"/>
                                        <Button Content="🗑️" 
                                                Command="{Binding DataContext.DeleteTenantCommand, RelativeSource={RelativeSource AncestorType=UserControl}}"
                                                CommandParameter="{Binding}"
                                                Style="{StaticResource SecondaryButton}"
                                                Padding="8,4"
                                                ToolTip="Sil"/>
                                    </StackPanel>
                                </DataTemplate>
                            </DataGridTemplateColumn.CellTemplate>
                        </DataGridTemplateColumn>
                    </DataGrid.Columns>
                </DataGrid>

                <!-- Empty State -->
                <StackPanel Grid.Row="1"
                            Visibility="{Binding FilteredTenants.Count, Converter={x:Static converters:EmptyStateConverter.Instance}}"
                            VerticalAlignment="Center" HorizontalAlignment="Center">
                    <TextBlock Text="👥" FontSize="48" HorizontalAlignment="Center" Opacity="0.3"/>
                    <TextBlock Text="Henüz kiracı eklenmemiş" 
                               FontSize="16" HorizontalAlignment="Center" 
                               Margin="0,10,0,5" Opacity="0.6"/>
                    <TextBlock Text="İlk kiracıyı eklemek için 'Yeni Kiracı' butonuna tıklayın" 
                               FontSize="12" HorizontalAlignment="Center" 
                               Opacity="0.5"/>
                </StackPanel>
            </Grid>
        </Border>
    </Grid>
</UserControl>
