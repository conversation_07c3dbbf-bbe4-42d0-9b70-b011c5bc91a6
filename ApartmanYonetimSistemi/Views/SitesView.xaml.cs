using System.Windows.Controls;
using System.Windows.Input;
using ApartmanYonetimSistemi.ViewModels;
using Microsoft.Extensions.DependencyInjection;
using System.Windows;

namespace ApartmanYonetimSistemi.Views
{
    public partial class SitesView : UserControl
    {
        public SitesView()
        {
            InitializeComponent();

            // ViewModel'i DI container'dan al
            var app = (App)Application.Current;
            DataContext = app.Services.GetRequiredService<SitesViewModel>();
        }

        private void ListView_MouseDoubleClick(object sender, MouseButtonEventArgs e)
        {
            // Double click ile site detayı açılabilir
            if (sender is ListView listView && listView.SelectedItem != null)
            {
                // Site detay sayfası açılacak
            }
        }
    }
}
