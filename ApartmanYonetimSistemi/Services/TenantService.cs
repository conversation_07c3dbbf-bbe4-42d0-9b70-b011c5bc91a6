using ApartmanYonetimSistemi.Data;
using ApartmanYonetimSistemi.Models;
using Microsoft.EntityFrameworkCore;

namespace ApartmanYonetimSistemi.Services
{
    public class TenantService : ITenantService
    {
        private readonly ApartmanDbContext _context;

        public TenantService(ApartmanDbContext context)
        {
            _context = context;
        }

        public async Task<IEnumerable<Tenant>> GetAllAsync()
        {
            return await _context.Tenants
                .Include(t => t.CurrentUnit)
                .Include(t => t.User)
                .Where(t => t.IsActive)
                .OrderBy(t => t.FirstName)
                .ThenBy(t => t.LastName)
                .ToListAsync();
        }

        public async Task<Tenant?> GetByIdAsync(string id)
        {
            return await _context.Tenants
                .Include(t => t.CurrentUnit)
                .Include(t => t.User)
                .Include(t => t.Payments)
                .FirstOrDefaultAsync(t => t.Id == id && t.IsActive);
        }

        public async Task<IEnumerable<Tenant>> GetByUserIdAsync(string userId)
        {
            return await _context.Tenants
                .Include(t => t.CurrentUnit)
                .Include(t => t.User)
                .Where(t => t.UserId == userId && t.IsActive)
                .OrderBy(t => t.FirstName)
                .ThenBy(t => t.LastName)
                .ToListAsync();
        }

        public async Task<IEnumerable<Tenant>> GetByCompanyIdAsync(string? companyId)
        {
            if (string.IsNullOrEmpty(companyId))
                return new List<Tenant>();

            return await _context.Tenants
                .Include(t => t.CurrentUnit)
                .ThenInclude(u => u!.Apartment)
                .ThenInclude(a => a.Site)
                .Include(t => t.User)
                .Where(t => t.CurrentUnit!.Apartment.Site.CompanyId == companyId && t.IsActive)
                .OrderBy(t => t.FirstName)
                .ThenBy(t => t.LastName)
                .ToListAsync();
        }

        public async Task<Tenant> CreateAsync(Tenant tenant)
        {
            tenant.Id = Guid.NewGuid().ToString();
            tenant.CreatedAt = DateTime.UtcNow;
            tenant.UpdatedAt = DateTime.UtcNow;
            tenant.IsActive = true;

            _context.Tenants.Add(tenant);
            await _context.SaveChangesAsync();
            return tenant;
        }

        public async Task<Tenant> UpdateAsync(Tenant tenant)
        {
            tenant.UpdatedAt = DateTime.UtcNow;
            _context.Tenants.Update(tenant);
            await _context.SaveChangesAsync();
            return tenant;
        }

        public async Task DeleteAsync(string id)
        {
            var tenant = await _context.Tenants.FindAsync(id);
            if (tenant != null)
            {
                tenant.IsActive = false;
                tenant.UpdatedAt = DateTime.UtcNow;
                await _context.SaveChangesAsync();
            }
        }
    }
}
